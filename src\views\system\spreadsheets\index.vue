<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
                  <el-form-item label="电子表格标题" prop="title">
                    <el-input
                        v-model="queryParams.title"
                        placeholder="请输入电子表格标题"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="电子表格标识" prop="slug">
                    <el-input
                        v-model="queryParams.slug"
                        placeholder="请输入电子表格标识"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="更新周期" prop="updateCycle">
                    <el-input
                        v-model="queryParams.updateCycle"
                        placeholder="请输入更新周期"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:spreadsheets:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:spreadsheets:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:spreadsheets:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:spreadsheets:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="spreadsheetsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center"  type="index" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="电子表格标题" align="center" prop="title" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="电子表格标识" align="center" prop="slug" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="电子表格简介" align="center" prop="description" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="电子表格跳转链接" align="center" prop="joinLink" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="电子表格图片" align="center" prop="imageUrl" width="100" v-if="columns[5].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.imageUrl" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createAt" width="180" v-if="columns[6].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="最后更新时间" align="center" prop="updateAt" width="180" v-if="columns[7].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.updateAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="产品数量" align="center" prop="productCount" v-if="columns[8].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="更新周期" align="center" prop="updateCycle" v-if="columns[9].visible" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:spreadsheets:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:spreadsheets:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改agt电子表格对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="spreadsheetsRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="电子表格标题" prop="title">
                          <el-input v-model="form.title" placeholder="请输入电子表格标题" />
                        </el-form-item>
                        <el-form-item label="电子表格标识" prop="slug">
                          <el-input v-model="form.slug" placeholder="请输入电子表格标识" />
                        </el-form-item>
                        <el-form-item label="电子表格简介" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="电子表格跳转链接" prop="joinLink">
                          <el-input v-model="form.joinLink" placeholder="请输入电子表格跳转链接" />
                        </el-form-item>
                        <el-form-item label="电子表格图片" prop="imageUrl">
                          <image-upload v-model="form.imageUrl"/>
                        </el-form-item>
                        <el-form-item label="产品数量" prop="productCount">
                          <el-input v-model="form.productCount" placeholder="请输入产品数量" />
                        </el-form-item>
                        <el-form-item label="更新周期" prop="updateCycle">
                          <el-input v-model="form.updateCycle" placeholder="请输入更新周期" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Spreadsheets">
  import { listSpreadsheets, getSpreadsheets, delSpreadsheets, addSpreadsheets, updateSpreadsheets } from "@/api/system/spreadsheets";

  const { proxy } = getCurrentInstance();

  const spreadsheetsList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    title: null,
                    slug: null,
                    updateCycle: null
    },
    rules: {
                    title: [
                { required: true, message: "电子表格标题不能为空", trigger: "blur" }
              ],
                    slug: [
                { required: true, message: "电子表格标识不能为空", trigger: "blur" }
              ],
                    description: [
                { required: true, message: "电子表格简介不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '主键id', visible: true },
                { key: 1, label: '电子表格标题', visible: true },
                { key: 2, label: '电子表格标识', visible: true },
                { key: 3, label: '电子表格简介', visible: true },
                { key: 4, label: '电子表格跳转链接', visible: true },
                { key: 5, label: '电子表格图片', visible: true },
                { key: 6, label: '创建时间', visible: true },
                { key: 7, label: '最后更新时间', visible: true },
                { key: 8, label: '产品数量', visible: true },
                { key: 9, label: '更新周期', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询agt电子表格列表 */
  function getList() {
    loading.value = true;
    listSpreadsheets(queryParams.value).then(response => {
            spreadsheetsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    title: null,
                    slug: null,
                    description: null,
                    joinLink: null,
                    imageUrl: null,
                    createAt: null,
                    updateAt: null,
                    productCount: null,
                    updateCycle: null
    };
    proxy.resetForm("spreadsheetsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加agt电子表格";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getSpreadsheets(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改agt电子表格";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["spreadsheetsRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateSpreadsheets(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addSpreadsheets(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除agt电子表格编号为"' + _ids + '"的数据项？').then(function() {
      return delSpreadsheets(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/spreadsheets/export', {
      ...queryParams.value
    }, `spreadsheets_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
