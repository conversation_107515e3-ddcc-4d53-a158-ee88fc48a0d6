import request from '@/utils/request'

// 查询agt电子表格列表
export function listSpreadsheets(query) {
  return request({
    url: '/system/spreadsheets/list',
    method: 'get',
    params: query
  })
}

// 查询agt电子表格详细
export function getSpreadsheets(id) {
  return request({
    url: '/system/spreadsheets/' + id,
    method: 'get'
  })
}

// 新增agt电子表格
export function addSpreadsheets(data) {
  return request({
    url: '/system/spreadsheets',
    method: 'post',
    data: data
  })
}

// 修改agt电子表格
export function updateSpreadsheets(data) {
  return request({
    url: '/system/spreadsheets',
    method: 'put',
    data: data
  })
}

// 删除agt电子表格
export function delSpreadsheets(id) {
  return request({
    url: '/system/spreadsheets/' + id,
    method: 'delete'
  })
}
