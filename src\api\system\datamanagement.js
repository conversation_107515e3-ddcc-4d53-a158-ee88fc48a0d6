import request from '@/utils/request'

// 查询omg资料列表
export function listDatamanagement(query) {
  return request({
    url: '/system/datamanagement/list',
    method: 'get',
    params: query
  })
}

// 查询omg资料详细
export function getDatamanagement(id) {
  return request({
    url: '/system/datamanagement/' + id,
    method: 'get'
  })
}

// 新增omg资料
export function addDatamanagement(data) {
  return request({
    url: '/system/datamanagement',
    method: 'post',
    data: data
  })
}

// 修改omg资料
export function updateDatamanagement(data) {
  return request({
    url: '/system/datamanagement',
    method: 'put',
    data: data
  })
}

// 删除omg资料
export function delDatamanagement(id) {
  return request({
    url: '/system/datamanagement/' + id,
    method: 'delete'
  })
}
