import request from '@/utils/request'

// 查询omg首页视频组列表
export function listOmgHomeVideo(query) {
  return request({
    url: '/system/OmgHomeVideo/list',
    method: 'get',
    params: query
  })
}

// 查询omg首页视频组详细
export function getOmgHomeVideo(id) {
  return request({
    url: '/system/OmgHomeVideo/' + id,
    method: 'get'
  })
}

// 新增omg首页视频组
export function addOmgHomeVideo(data) {
  return request({
    url: '/system/OmgHomeVideo',
    method: 'post',
    data: data
  })
}

// 修改omg首页视频组
export function updateOmgHomeVideo(data) {
  return request({
    url: '/system/OmgHomeVideo',
    method: 'put',
    data: data
  })
}

// 删除omg首页视频组
export function delOmgHomeVideo(id) {
  return request({
    url: '/system/OmgHomeVideo/' + id,
    method: 'delete'
  })
}
