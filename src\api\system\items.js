import request from '@/utils/request'

// 查询积分兑换商品列表
export function listItems(query) {
  return request({
    url: '/system/items/list',
    method: 'get',
    params: query
  })
}

// 查询积分兑换商品详细
export function getItems(id) {
  return request({
    url: '/system/items/' + id,
    method: 'get'
  })
}

// 新增积分兑换商品
export function addItems(data) {
  return request({
    url: '/system/items',
    method: 'post',
    data: data
  })
}

// 修改积分兑换商品
export function updateItems(data) {
  return request({
    url: '/system/items',
    method: 'put',
    data: data
  })
}

// 删除积分兑换商品
export function delItems(id) {
  return request({
    url: '/system/items/' + id,
    method: 'delete'
  })
}
