<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="名称" prop="productName">
                    <el-input
                        v-model="queryParams.productName"
                        placeholder="请输入名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="价格" prop="price">
                    <el-input
                        v-model="queryParams.price"
                        placeholder="请输入价格"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-date-picker clearable
                                    v-model="queryParams.createdAt"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择创建时间">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgSummerProducts:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgSummerProducts:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgSummerProducts:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgSummerProducts:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgSummerProductsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="名称" align="center" prop="productName" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="价格" align="center" prop="price" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="浏览数" align="center" prop="views" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="点赞量" align="center" prop="likes" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="评论数" align="center" prop="comments" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="所属平台" align="center" prop="platform" v-if="columns[9].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="收藏量" align="center" prop="favorites" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="图片URL" align="center" prop="imageUrl" width="100" v-if="columns[7].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.imageUrl" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="状态" align="center" prop="status" width="100">
                  <template #default="scope">
                    <span class="status-tag">
                      <span class="status-dot" :class="isActive(scope.row.status) ? 'status-dot-green' : 'status-dot-red'"></span>
                      {{ isActive(scope.row.status) ? '上架' : '下架' }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createdAt" width="180" v-if="columns[8].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgSummerProducts:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgSummerProducts:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改夏季新品对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgSummerProductsRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="名称" prop="productName">
                          <el-input v-model="form.productName" placeholder="请输入名称" />
                        </el-form-item>
                        <el-form-item label="价格" prop="price">
                          <el-input v-model="form.price" placeholder="请输入价格" />
                        </el-form-item>
                        <el-form-item label="浏览数" prop="views">
                          <el-input v-model="form.views" placeholder="请输入浏览数" />
                        </el-form-item>
                        <el-form-item label="点赞量" prop="likes">
                          <el-input v-model="form.likes" placeholder="请输入点赞量" />
                        </el-form-item>
                        <el-form-item label="评论数" prop="comments">
                          <el-input v-model="form.comments" placeholder="请输入评论数" />
                        </el-form-item>
                        <el-form-item label="收藏量" prop="favorites">
                          <el-input v-model="form.favorites" placeholder="请输入收藏量" />
                        </el-form-item>
                        <el-form-item label="图片URL" prop="imageUrl">
                          <image-upload v-model="form.imageUrl"/>
                        </el-form-item>
                        <el-form-item label="状态" prop="status">
                          <el-select v-model="form.status" placeholder="请选择状态">
                            <el-option :value="'active'" label="上架" />
                            <el-option :value="'inactive'" label="下架" />
                          </el-select>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgSummerProducts">
  import { listOmgSummerProducts, getOmgSummerProducts, delOmgSummerProducts, addOmgSummerProducts, updateOmgSummerProducts } from "@/api/system/OmgSummerProducts";

  const { proxy } = getCurrentInstance();

  const OmgSummerProductsList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    productName: null,
                    price: null,
                    createdAt: null
    },
    rules: {
    },
    //表格展示列
    columns: [
              { key: 0, label: '夏季新品id', visible: true },
                { key: 1, label: '名称', visible: true },
                { key: 2, label: '价格', visible: true },
                { key: 3, label: '浏览数', visible: true },
                { key: 4, label: '点赞量', visible: true },
                { key: 5, label: '评论数', visible: true },
                { key: 6, label: '收藏量', visible: true },
                { key: 7, label: '图片URL', visible: true },
                { key: 8, label: '创建时间', visible: true },
                { key: 9, label: '所属平台', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询夏季新品列表 */
  function getList() {
    loading.value = true;
    listOmgSummerProducts(queryParams.value).then(response => {
            OmgSummerProductsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    productName: null,
                    price: null,
                    views: null,
                    likes: null,
                    comments: null,
                    favorites: null,
                    imageUrl: null,
                    createdAt: null,
                    status: 'active'
    };
    proxy.resetForm("OmgSummerProductsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加夏季新品";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getOmgSummerProducts(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改夏季新品";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgSummerProductsRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateOmgSummerProducts(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgSummerProducts(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除夏季新品编号为"' + _ids + '"的数据项？').then(function() {
      return delOmgSummerProducts(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgSummerProducts/export', {
      ...queryParams.value
    }, `OmgSummerProducts_${new Date().getTime()}.xlsx`)
  }

  // 状态判断函数，'active' 为上架
  const isActive = (status) => {
    return status === 'active';
  };

  getList();
</script>

<style scoped>
.status-tag {
  display: inline-flex;
  align-items: center;
}
.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
}
.status-dot-green {
  background-color: #67c23a;
}
.status-dot-red {
  background-color: #f56c6c;
}
</style>
