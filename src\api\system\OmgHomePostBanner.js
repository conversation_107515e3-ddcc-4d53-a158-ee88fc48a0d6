import request from '@/utils/request'

// 查询轮播图列表
export function listOmgHomePostBanner(query) {
  return request({
    url: '/system/OmgHomePostBanner/list',
    method: 'get',
    params: query
  })
}

// 查询轮播图详细
export function getOmgHomePostBanner(id) {
  return request({
    url: '/system/OmgHomePostBanner/' + id,
    method: 'get'
  })
}

// 新增轮播图
export function addOmgHomePostBanner(data) {
  return request({
    url: '/system/OmgHomePostBanner',
    method: 'post',
    data: data
  })
}

// 修改轮播图
export function updateOmgHomePostBanner(data) {
  return request({
    url: '/system/OmgHomePostBanner',
    method: 'put',
    data: data
  })
}

// 删除轮播图
export function delOmgHomePostBanner(id) {
  return request({
    url: '/system/OmgHomePostBanner/' + id,
    method: 'delete'
  })
}
