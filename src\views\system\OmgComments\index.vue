<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="帖子id" prop="postId">
                    <el-input
                        v-model="queryParams.postId"
                        placeholder="请输入帖子id"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="用户名" prop="username">
                    <el-input
                        v-model="queryParams.username"
                        placeholder="请输入用户名"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="父评论 ID" prop="parentCommentId">
                    <el-input
                        v-model="queryParams.parentCommentId"
                        placeholder="请输入父评论 ID"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="点赞数" prop="likeCount">
                    <el-input
                        v-model="queryParams.likeCount"
                        placeholder="请输入点赞数"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-date-picker clearable
                                    v-model="queryParams.createdAt"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择创建时间">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgComments:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgComments:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgComments:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgComments:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgCommentsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="帖子id" align="center" prop="postId" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户id" align="center" prop="users.username" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="评论内容" align="center" prop="postContent" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="父评论 ID" align="center" prop="parentCommentId" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="图片URL" align="center" prop="imageUrls" width="80" v-if="columns[5].visible">
                  <template #default="scope">
                    <div v-if="parseImageUrls(scope.row.imageUrls).length">
                      <el-carousel
                        :interval="3000"
                        arrow="always"
                        height="60px"
                        indicator-position="none"
                        style="width: 60px; margin: 0 auto;"
                      >
                        <el-carousel-item
                          v-for="(url, index) in parseImageUrls(scope.row.imageUrls)"
                          :key="index"
                          style="display: flex; align-items: center; justify-content: center; height: 60px;"
                        >
                          <image-preview :src="url" :width="56" :height="56" style="border-radius: 6px;" />
                        </el-carousel-item>
                      </el-carousel>
                    </div>
                    <span v-else>无图片</span>
                  </template>
                </el-table-column>
                <el-table-column label="点赞数" align="center" prop="likeCount" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="创建时间" align="center" prop="createdAt" width="180" v-if="columns[7].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <!-- <el-table-column label="修改时间" align="center" prop="updatedAt" width="180" v-if="columns[8].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column> -->
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgComments:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgComments:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改帖子评论对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgCommentsRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="帖子id" prop="postId">
                          <el-input v-model="form.postId" placeholder="请输入帖子id" />
                        </el-form-item>
                        <el-form-item label="用户" prop="userId">
                          <el-select v-model="form.userId" filterable placeholder="请选择用户">
                            <el-option
                              v-for="item in usersOptions"
                              :key="item.value"
                              :label="item.label"
                              :value="item.value"
                            />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="评论内容">
                          <editor v-model="form.postContent" :min-height="192"/>
                        </el-form-item>
                        <el-form-item label="父评论 ID" prop="parentCommentId">
                          <el-input v-model="form.parentCommentId" placeholder="请输入父评论 ID" />
                        </el-form-item>
                        <el-form-item label="图片URL" prop="imageUrls">
                          <image-upload v-model="form.imageUrls"/>
                        </el-form-item>
                        <el-form-item label="点赞数" prop="likeCount">
                          <el-input v-model="form.likeCount" placeholder="请输入点赞数" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgComments">
  import { listOmgComments, getOmgComments, delOmgComments, addOmgComments, updateOmgComments } from "@/api/system/OmgComments";
  import { listUsers } from "@/api/system/users";
  import { ref, onMounted } from 'vue';

  const { proxy } = getCurrentInstance();

  const OmgCommentsList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const usersOptions = ref([]);
  function fetchUsersOptions() {
    listUsers({ pageNum: 1, pageSize: 9999 }).then(res => {
      usersOptions.value = (res.rows || []).map(u => ({
        label: u.username,
        value: u.userId
      }));
    });
  }
  onMounted(() => {
    fetchUsersOptions();
  });

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      postId: null,
      username: '',
      postContent: null,
      parentCommentId: null,
      imageUrls: null,
      likeCount: null,
      createdAt: null,
    },
    rules: {
    },
    //表格展示列
    columns: [
              { key: 0, label: '评论唯一标识', visible: true },
                { key: 1, label: '帖子id', visible: true },
                { key: 2, label: '用户id', visible: true },
                { key: 3, label: '评论内容', visible: true },
                { key: 4, label: '父评论 ID', visible: true },
                { key: 5, label: '图片URL', visible: true },
                { key: 6, label: '点赞数', visible: true },
                { key: 7, label: '创建时间', visible: true },
                { key: 8, label: '修改时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  // 解析 imageUrls 字段为数组
  const parseImageUrls = (urls) => {
    if (!urls) return [];
    try {
      const arr = JSON.parse(urls);
      if (Array.isArray(arr)) return arr;
      return [];
    } catch (e) {
      // 兜底：如果是逗号分隔字符串
      if (typeof urls === 'string') {
        return urls.split(',').map(s => s.trim()).filter(Boolean);
      }
      return [];
    }
  };

  /** 查询帖子评论列表 */
  function getList() {
    loading.value = true;
    listOmgComments(queryParams.value).then(response => {
            OmgCommentsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    postId: null,
                    userId: null,
                    postContent: null,
                    parentCommentId: null,
                    imageUrls: null,
                    likeCount: null,
                    createdAt: null,
                    updatedAt: null
    };
    proxy.resetForm("OmgCommentsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加帖子评论";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getOmgComments(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改帖子评论";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgCommentsRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateOmgComments(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgComments(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除帖子评论编号为"' + _ids + '"的数据项？').then(function() {
      return delOmgComments(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgComments/export', {
      ...queryParams.value
    }, `OmgComments_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
