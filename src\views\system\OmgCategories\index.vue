<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
                  <el-form-item label="分类名称" prop="name">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入分类名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="分类标识符" prop="slug">
                    <el-input
                        v-model="queryParams.slug"
                        placeholder="请输入分类标识符"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgCategories:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgCategories:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgCategories:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgCategories:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgCategoriesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="分类唯一标识" align="center" prop="categoryId" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="分类名称" align="center" prop="name" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="分类标识符" align="center" prop="slug" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="分类详细描述" align="center" prop="description" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="分类图片链接" align="center" prop="imageUrl" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="显示顺序" align="center" prop="displayOrder" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="分类创建时间" align="center" prop="createdAt" width="180" v-if="columns[6].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgCategories:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgCategories:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg_分类对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgCategoriesRef" :model="form" :rules="rules" label-width="100px">
                        <el-form-item label="分类名称" prop="name">
                          <el-input v-model="form.name" placeholder="请输入分类名称" />
                        </el-form-item>
                        <el-form-item label="分类标识符" prop="slug">
                          <el-input v-model="form.slug" placeholder="请输入分类标识符" />
                        </el-form-item>
                        <el-form-item label="分类详细描述" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="分类图片链接" prop="imageUrl">
                          <el-input v-model="form.imageUrl" placeholder="请输入分类图片链接" />
                        </el-form-item>
                        <el-form-item label="显示顺序" prop="displayOrder">
                          <el-input v-model="form.displayOrder" placeholder="请输入显示顺序" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgCategories">
  import { listOmgCategories, getOmgCategories, delOmgCategories, addOmgCategories, updateOmgCategories } from "@/api/system/OmgCategories";

  const { proxy } = getCurrentInstance();

  const OmgCategoriesList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    name: null,
                    slug: null,
    },
    rules: {
                    name: [
                { required: true, message: "分类名称不能为空", trigger: "blur" }
              ],
                    slug: [
                { required: true, message: "分类标识符不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '分类唯一标识', visible: true },
                { key: 1, label: '分类名称', visible: true },
                { key: 2, label: '分类标识符', visible: true },
                { key: 3, label: '分类详细描述', visible: true },
                { key: 4, label: '分类图片链接', visible: true },
                { key: 5, label: '显示顺序', visible: true },
                { key: 6, label: '分类创建时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg_分类列表 */
  function getList() {
    loading.value = true;
    listOmgCategories(queryParams.value).then(response => {
            OmgCategoriesList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    categoryId: null,
                    name: null,
                    slug: null,
                    description: null,
                    imageUrl: null,
                    displayOrder: null,
                    createdAt: null,
                    updatedAt: null
    };
    proxy.resetForm("OmgCategoriesRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.categoryId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加omg_分类";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _categoryId = row.categoryId || ids.value
    getOmgCategories(_categoryId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改omg_分类";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgCategoriesRef"].validate(valid => {
      if (valid) {
        if (form.value.categoryId != null) {
          updateOmgCategories(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgCategories(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _categoryIds = row.categoryId || ids.value;
    proxy.$modal.confirm('是否确认删除omg_分类编号为"' + _categoryIds + '"的数据项？').then(function() {
      return delOmgCategories(_categoryIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgCategories/export', {
      ...queryParams.value
    }, `OmgCategories_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
