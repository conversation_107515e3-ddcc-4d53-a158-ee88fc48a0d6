import request from '@/utils/request'

// 查询商品管理列表
export function listProducts(query) {
  return request({
    url: '/system/products/list',
    method: 'get',
    params: query
  })
}

// 获取商家全部商品列表
export function getSellerProducts(sellerId) {
  return request({
    url: '/system/products/seller/' + sellerId,
    method: 'get',
  })
}

// 查询商品管理详细
export function getProducts(productId) {
  return request({
    url: '/system/products/' + productId,
    method: 'get'
  })
}

// 新增商品管理
export function addProducts(data) {
  return request({
    url: '/system/products',
    method: 'post',
    data: data
  })
}

// 修改商品管理
export function updateProducts(data) {
  return request({
    url: '/system/products',
    method: 'put',
    data: data
  })
}

// 删除商品管理
export function delProducts(productId) {
  return request({
    url: '/system/products/' + productId,
    method: 'delete'
  })
}

// 修改商品状态
export function updateProductStatus(productId, status) {
  return request({
    url: `/system/products/status`,
    method: 'put',
    data: { productId, status }
  });
}

// 获取商家展示商品
export function getSellerDisplayProducts(sellerId) {
  return request({
    url: `/system/products/seller/${sellerId}/display`,
    method: 'get'
  });
}

// 设置商家展示商品
export function setSellerDisplayProducts(data) {
  return request({
    url: '/system/products/seller/display',
    method: 'post',
    data: data
  });
}

// 导入商品数据
export function importProducts(file) {
  const formData = new FormData();
  formData.append('file', file);
  return request({
    url: '/system/products/importData',
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

// 设置每日一品
export function addDailyProduct(productId) {
  return request({
    url: '/system/dailyProduct/addOrUpdate',
    method: 'post',
    data: { productId }
  })
}

// 检查今日是否已存在每日一品
export function checkExistTodayDailyProduct() {
  return request({
    url: '/system/dailyProduct/checkExist',
    method: 'get'
  })
}

// 更新每日一品
export function updateDailyProduct(productId) {
  return request({
    url: '/system/dailyProduct/update',
    method: 'post',
    data: { productId }
  })
}

// 设置最热商品
export function addHotProducts(productIds) {
  // 转换格式为后端需要的数组格式
  const data = productIds.map(id => ({ productId: id }));
  return request({
    url: '/system/Hotproduct/batchInsert',
    method: 'post',
    data: data
  })
}

// 检查今日是否已存在最热商品
export function checkExistHotProducts() {
  return request({
    url: '/system/Hotproduct/checkExist',
    method: 'get'
  })
}

// 获取当前热门商品列表
export function getCurrentHotProducts() {
  return request({
    url: '/system/Hotproduct/list',
    method: 'get'
  })
}

// 更新最热商品
export function updateHotProducts(productIds, existingHotProducts) {
  // 合并现有记录ID和新选择的商品ID
  const data = productIds.map((id, index) => {
    // 如果existingHotProducts有对应索引的记录，使用它的id
    if (existingHotProducts && existingHotProducts[index]) {
      return {
        id: existingHotProducts[index].id,
        productId: id
      }
    }
    // 如果没有对应的记录，只使用productId
    return { productId: id }
  });

  return request({
    url: '/system/Hotproduct/batchUpdate',
    method: 'post',
    data: data
  })
}

// 检查今日是否已存在最新商品
export function checkExistCurrentProducts() {
  return request({
    url: '/system/CurrentProducts/checkExist',
    method: 'get'
  })
}

// 获取当前最新商品列表
export function getCurrentNewProducts() {
  return request({
    url: '/system/CurrentProducts/list',
    method: 'get'
  })
}

// 设置最新商品
export function addCurrentProducts(productIds) {
  // 转换格式为后端需要的数组格式
  const data = productIds.map(id => ({ productId: id }));
  return request({
    url: '/system/CurrentProducts/batchInsert',
    method: 'post',
    data: data
  })
}

// 更新最新商品
export function updateCurrentProducts(productIds, existingProducts) {
  // 合并现有记录ID和新选择的商品ID
  const data = productIds.map((id, index) => {
    // 如果existingProducts有对应索引的记录，使用它的id
    if (existingProducts && existingProducts[index]) {
      return {
        id: existingProducts[index].id,
        productId: id
      }
    }
    // 如果没有对应的记录，只使用productId
    return { productId: id }
  });

  return request({
    url: '/system/CurrentProducts/batchUpdate',
    method: 'post',
    data: data
  })
}
