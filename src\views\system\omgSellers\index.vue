<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="店铺名称" prop="storeName">
                    <el-input
                        v-model="queryParams.storeName"
                        placeholder="请输入店铺名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="店铺状态" prop="status">
                    <el-select v-model="queryParams.status" style="width: 200px" placeholder="请选择店铺状态" clearable>
                      <el-option
                          v-for="dict in omg_sellers_status"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:omgSellers:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:omgSellers:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:omgSellers:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:omgSellers:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="omgSellersList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="卖家唯一标识" align="center" prop="sellerId" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺名称" align="center" prop="storeName" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺链接" align="center" prop="storeSlug" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺Logo链接" align="center" prop="logo" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺封面图片链接" align="center" prop="coverImage" width="100" v-if="columns[4].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.coverImage" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="店铺描述" align="center" prop="description" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺联系邮箱" align="center" prop="contactEmail" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺入驻时间" align="center" prop="joinedDate" width="180" v-if="columns[7].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.joinedDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="平台专属折扣码" align="center" prop="platformDiscountCode" v-if="columns[8].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="平台链接" align="center" prop="platformUrl" v-if="columns[9].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺状态" align="center" prop="status" v-if="columns[10].visible">
                <template #default="scope">
                      <dict-tag :options="omg_sellers_status" :value="scope.row.status"/>
                </template>
              </el-table-column>
                <el-table-column label="店铺类型" align="center" prop="type" v-if="columns[11].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="点赞" align="center" prop="likes" v-if="columns[12].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="收藏" align="center" prop="collect" v-if="columns[13].visible" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:omgSellers:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:omgSellers:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg_卖家店铺对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="omgSellersRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="店铺名称" prop="storeName">
                          <el-input v-model="form.storeName" placeholder="请输入店铺名称" />
                        </el-form-item>
                        <el-form-item label="店铺链接" prop="storeSlug">
                          <el-input v-model="form.storeSlug" placeholder="请输入店铺链接" />
                        </el-form-item>
                        <el-form-item label="店铺Logo链接" prop="logo">
                          <el-input v-model="form.logo" placeholder="请输入店铺Logo链接" />
                        </el-form-item>
                        <el-form-item label="店铺封面图片链接" prop="coverImage">
                          <image-upload v-model="form.coverImage"/>
                        </el-form-item>
                        <el-form-item label="店铺描述" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="店铺联系邮箱" prop="contactEmail">
                          <el-input v-model="form.contactEmail" placeholder="请输入店铺联系邮箱" />
                        </el-form-item>
                        <el-form-item label="平台专属折扣码" prop="platformDiscountCode">
                          <el-input v-model="form.platformDiscountCode" placeholder="请输入平台专属折扣码" />
                        </el-form-item>
                        <el-form-item label="平台链接" prop="platformUrl">
                          <el-input v-model="form.platformUrl" placeholder="请输入平台链接" />
                        </el-form-item>
                        <el-form-item label="店铺状态" prop="status">
                          <el-select v-model="form.status" placeholder="请选择店铺状态">
                            <el-option
                                v-for="dict in omg_sellers_status"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item label="店铺类型" prop="type">
                          <el-input v-model="form.type" placeholder="请输入店铺类型" />
                        </el-form-item>
                        <el-form-item label="点赞" prop="likes">
                          <el-input v-model="form.likes" placeholder="请输入点赞" />
                        </el-form-item>
                        <el-form-item label="收藏" prop="collect">
                          <el-input v-model="form.collect" placeholder="请输入收藏" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgSellers">
  import { listOmgSellers, getOmgSellers, delOmgSellers, addOmgSellers, updateOmgSellers } from "@/api/system/omgSellers";

  const { proxy } = getCurrentInstance();
      const { omg_sellers_status } = proxy.useDict('omg_sellers_status');

  const omgSellersList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    storeName: null,
                    status: null,
    },
    rules: {
                    storeName: [
                { required: true, message: "店铺名称不能为空", trigger: "blur" }
              ],
                    status: [
                { required: true, message: "店铺状态不能为空", trigger: "change" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '卖家唯一标识', visible: true },
                { key: 1, label: '店铺名称', visible: true },
                { key: 2, label: '店铺链接', visible: true },
                { key: 3, label: '店铺Logo链接', visible: true },
                { key: 4, label: '店铺封面图片链接', visible: true },
                { key: 5, label: '店铺描述', visible: true },
                { key: 6, label: '店铺联系邮箱', visible: true },
                { key: 7, label: '店铺入驻时间', visible: true },
                { key: 8, label: '平台专属折扣码', visible: true },
                { key: 9, label: '平台链接', visible: true },
                { key: 10, label: '店铺状态', visible: true },
                { key: 11, label: '店铺类型', visible: true },
                { key: 12, label: '点赞', visible: true },
                { key: 13, label: '收藏', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg_卖家店铺列表 */
  function getList() {
    loading.value = true;
    listOmgSellers(queryParams.value).then(response => {
            omgSellersList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    sellerId: null,
                    storeName: null,
                    storeSlug: null,
                    logo: null,
                    coverImage: null,
                    description: null,
                    contactEmail: null,
                    joinedDate: null,
                    platformDiscountCode: null,
                    platformUrl: null,
                    status: null,
                    type: null,
                    likes: null,
                    collect: null
    };
    proxy.resetForm("omgSellersRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.sellerId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加omg_卖家店铺";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _sellerId = row.sellerId || ids.value
    getOmgSellers(_sellerId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改omg_卖家店铺";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["omgSellersRef"].validate(valid => {
      if (valid) {
        if (form.value.sellerId != null) {
          updateOmgSellers(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgSellers(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _sellerIds = row.sellerId || ids.value;
    proxy.$modal.confirm('是否确认删除omg_卖家店铺编号为"' + _sellerIds + '"的数据项？').then(function() {
      return delOmgSellers(_sellerIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/omgSellers/export', {
      ...queryParams.value
    }, `omgSellers_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
