# AGTFIND后台管理系统

## 项目介绍

AGTFIND后台管理系统是由屿量科技开发的一套完整的企业级后台管理系统解决方案。该系统基于RuoYi-Vue框架进行定制开发，采用前后端分离架构，为管理人员提供了一个功能齐全、操作便捷的管理平台。

## 功能特点

- **完善的用户权限管理**：支持用户、角色、权限多层次管理
- **丰富的内置功能**：包含系统监控、数据统计、用户管理等多种功能模块
- **美观易用的界面**：基于Element Plus构建的现代化用户界面
- **灵活的配置选项**：支持多环境配置，满足不同部署场景需求
- **前端用户管理**：支持前端用户注册和管理功能

## 技术栈

- **前端框架**：Vue 3 + Vite
- **UI组件库**：Element Plus
- **状态管理**：Pinia
- **路由管理**：Vue Router
- **HTTP请求**：Axios
- **可视化图表**：ECharts
- **CSS预处理器**：Sass

## 开发环境

```bash
# 克隆项目
<NAME_EMAIL>:xmylkj/agtfind-admin.git

# 进入项目目录
cd agtfind-admin

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

## 生产环境部署

```bash
# 构建生产环境
npm run build:prod

# 构建测试环境
npm run build:stage

# 预览构建结果
npm run preview
```

## 项目结构

```
├── src                     # 源代码
│   ├── api                 # API请求
│   ├── assets              # 静态资源
│   ├── components          # 全局组件
│   ├── directive           # 全局指令
│   ├── layout              # 布局组件
│   ├── plugins             # 插件
│   ├── router              # 路由配置
│   ├── store               # 全局状态管理
│   ├── utils               # 工具函数
│   ├── views               # 视图页面
│   ├── App.vue             # 入口组件
│   ├── main.js             # 入口文件
│   └── permission.js       # 权限控制
├── public                  # 静态资源
├── vite                    # Vite配置文件
├── .env.development        # 开发环境配置
├── .env.production         # 生产环境配置
├── .env.staging            # 测试环境配置
├── vite.config.js          # Vite配置
└── package.json            # 项目依赖
```

## 浏览器支持

- 推荐使用Chrome、Firefox、Microsoft Edge等现代浏览器

## 许可证

[MIT](LICENSE)

## 联系方式

- 开发团队：屿量科技
