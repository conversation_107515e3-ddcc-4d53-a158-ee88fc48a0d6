import request from '@/utils/request'

// 查询omg_分类列表
export function listOmgCategories(query) {
  return request({
    url: '/system/OmgCategories/list',
    method: 'get',
    params: query
  })
}

// 查询omg_分类详细
export function getOmgCategories(categoryId) {
  return request({
    url: '/system/OmgCategories/' + categoryId,
    method: 'get'
  })
}

// 新增omg_分类
export function addOmgCategories(data) {
  return request({
    url: '/system/OmgCategories',
    method: 'post',
    data: data
  })
}

// 修改omg_分类
export function updateOmgCategories(data) {
  return request({
    url: '/system/OmgCategories',
    method: 'put',
    data: data
  })
}

// 删除omg_分类
export function delOmgCategories(categoryId) {
  return request({
    url: '/system/OmgCategories/' + categoryId,
    method: 'delete'
  })
}
