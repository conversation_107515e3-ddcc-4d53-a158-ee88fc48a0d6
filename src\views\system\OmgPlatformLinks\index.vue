<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="平台名称" prop="name">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入平台名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgPlatformLinks:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgPlatformLinks:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgPlatformLinks:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgPlatformLinks:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgPlatformLinksList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" type="index" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="平台名称" align="center" prop="name" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="平台logo" align="center" prop="logo" width="100" v-if="columns[2].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.logo" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="优惠券" align="center" prop="coupon" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="汇率" align="center" prop="exchangeRate" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="平台链接" align="center" prop="urltemplate" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="拼接字段" align="center" prop="concatenateFiled" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="显示顺序" align="center" prop="sort" v-if="columns[7].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="创建时间" align="center" prop="createAt" width="180" v-if="columns[8].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createAt, '{y}-{m}-{d}') }}</span>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgPlatformLinks:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgPlatformLinks:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改OMG平台链接对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgPlatformLinksRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="平台名称" prop="name">
                          <el-input v-model="form.name" placeholder="请输入平台名称" />
                        </el-form-item>
                        <el-form-item label="平台logo" prop="logo">
                          <image-upload v-model="form.logo"/>
                        </el-form-item>
                        <el-form-item label="优惠券" prop="coupon">
                          <el-input v-model="form.coupon" placeholder="请输入优惠券" />
                        </el-form-item>
                        <el-form-item label="汇率" prop="exchangeRate">
                          <el-input-number v-model="form.exchangeRate" :precision="4" :step="0.1" :min="0" placeholder="请输入汇率" style="width: 100%" />
                        </el-form-item>
                        <el-form-item label="平台链接" prop="urltemplate">
                          <el-input v-model="form.urltemplate" placeholder="请输入平台链接" />
                        </el-form-item>
                        <el-form-item label="拼接字段" prop="concatenateFiled">
                          <el-input v-model="form.concatenateFiled" placeholder="请输入拼接字段" />
                        </el-form-item>
                        <el-form-item label="显示顺序" prop="sort">
                          <el-input-number v-model="form.sort" :min="0" :max="999" placeholder="请输入显示顺序" style="width: 100%" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgPlatformLinks">
  import { listOmgPlatformLinks, getOmgPlatformLinks, delOmgPlatformLinks, addOmgPlatformLinks, updateOmgPlatformLinks } from "@/api/system/OmgPlatformLinks";

  const { proxy } = getCurrentInstance();

  const OmgPlatformLinksList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    name: null,
    },
    rules: {
                    name: [
                { required: true, message: "平台名称不能为空", trigger: "blur" }
              ],
                    sort: [
                { required: true, message: "显示顺序不能为空", trigger: "blur" },
                { type: "number", message: "显示顺序必须为数字", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '序号', visible: true },
                { key: 1, label: '平台名称', visible: true },
                { key: 2, label: '平台logo', visible: true },
                { key: 3, label: '优惠券', visible: true },
                { key: 4, label: '汇率', visible: true },
                { key: 5, label: '平台链接', visible: true },
                { key: 6, label: '拼接字段', visible: true },
                { key: 7, label: '显示顺序', visible: true },
                { key: 8, label: '创建时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询OMG平台链接列表 */
  function getList() {
    loading.value = true;
    listOmgPlatformLinks(queryParams.value).then(response => {
            OmgPlatformLinksList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    name: null,
                    logo: null,
                    coupon: null,
                    exchangeRate: null,
                    urltemplate: null,
                    concatenateFiled: null,
                    sort: null,
                    createAt: null
    };
    proxy.resetForm("OmgPlatformLinksRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加OMG平台链接";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getOmgPlatformLinks(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改OMG平台链接";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgPlatformLinksRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateOmgPlatformLinks(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgPlatformLinks(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除OMG平台链接编号为"' + _ids + '"的数据项？').then(function() {
      return delOmgPlatformLinks(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgPlatformLinks/export', {
      ...queryParams.value
    }, `OmgPlatformLinks_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
