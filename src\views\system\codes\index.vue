<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="邀请码" prop="inviteCode">
                    <el-input
                        v-model="queryParams.inviteCode"
                        placeholder="请输入邀请码"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="邀请码名称" prop="codeName">
                    <el-input
                        v-model="queryParams.codeName"
                        placeholder="请输入邀请码名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-date-picker clearable
                                    v-model="queryParams.createdAt"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择创建时间">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计图表区域 -->
    <el-card class="mb8" shadow="hover">
      <template #header>
        <div class="card-header">
          <span>邀请统计图表</span>
          <div class="chart-controls">
            <el-select
              v-model="chartTimeDimension"
              placeholder="选择时间维度"
              style="width: 120px; margin-right: 10px;"
              @change="updateChart"
            >
              <el-option label="按日统计" value="daily" />
              <el-option label="按周统计" value="weekly" />
              <el-option label="按月统计" value="monthly" />
            </el-select>
            <el-date-picker
              v-model="chartDateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              value-format="YYYY-MM-DD"
              @change="updateChart"
              style="margin-right: 10px;"
            />
            <el-input
              v-model="chartInviteCode"
              placeholder="请输入邀请码"
              style="width: 200px; margin-right: 10px;"
              @change="updateChart"
            />
            <el-button type="primary" @click="updateChart">刷新图表</el-button>
          </div>
        </div>
      </template>
      <div ref="chartContainer" style="width: 100%; height: 450px;"></div>
    </el-card>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:codes:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:codes:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:codes:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:codes:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="codesList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="${comment}" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="邀请码" align="center" prop="inviteCode" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="邀请码名称" align="center" prop="codeName" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="总访问次数" align="center" prop="totalVisits" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="总注册人数" align="center" prop="totalRegistrations" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="状态：active/inactive" align="center" prop="status" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="创建时间" align="center" prop="createdAt" width="180" v-if="columns[6].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:codes:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:codes:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改邀请码对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="codesRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="邀请码" prop="inviteCode">
                          <el-input v-model="form.inviteCode" placeholder="请输入邀请码" />
                        </el-form-item>
                        <el-form-item label="邀请码名称" prop="codeName">
                          <el-input v-model="form.codeName" placeholder="请输入邀请码名称" />
                        </el-form-item>
                        <el-form-item label="总访问次数" prop="totalVisits">
                          <el-input v-model="form.totalVisits" placeholder="请输入总访问次数" />
                        </el-form-item>
                        <el-form-item label="总注册人数" prop="totalRegistrations">
                          <el-input v-model="form.totalRegistrations" placeholder="请输入总注册人数" />
                        </el-form-item>
                        <el-form-item label="创建时间" prop="createdAt">
                          <el-date-picker clearable
                                          v-model="form.createdAt"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择创建时间">
                          </el-date-picker>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Codes">
  import { listCodes, getCodes, delCodes, addCodes, updateCodes } from "@/api/system/codes";
  import { getInviteTimeStats } from "@/api/system/invite";
  import * as echarts from 'echarts';

  const { proxy } = getCurrentInstance();

  const codesList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  // 图表相关变量
  const chartContainer = ref(null);
  const chartInstance = ref(null);
  const chartDateRange = ref([]);
  const chartInviteCode = ref('');
  const chartTimeDimension = ref('daily'); // 默认按日统计

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    inviteCode: null,
                    codeName: null,
                    status: null,
                    createdAt: null
    },
    rules: {
                    inviteCode: [
                { required: true, message: "邀请码不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '${comment}', visible: true },
                { key: 1, label: '邀请码', visible: true },
                { key: 2, label: '邀请码名称', visible: true },
                { key: 3, label: '总访问次数', visible: true },
                { key: 4, label: '总注册人数', visible: true },
                { key: 5, label: '状态：active/inactive', visible: true },
                { key: 6, label: '创建时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询邀请码列表 */
  function getList() {
    loading.value = true;
    listCodes(queryParams.value).then(response => {
            codesList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  /** 初始化图表 */
  function initChart() {
    if (chartContainer.value) {
      chartInstance.value = echarts.init(chartContainer.value);
      updateChart();
    }
  }

  /** 更新图表数据 */
  function updateChart() {
    if (!chartInstance.value) return;

    const params = {};

    // 添加日期范围参数
    if (chartDateRange.value && chartDateRange.value.length === 2) {
      params.startDate = chartDateRange.value[0];
      params.endDate = chartDateRange.value[1];
    }

    // 添加邀请码参数
    if (chartInviteCode.value) {
      params.inviteCode = chartInviteCode.value;
    }

    // 调用API获取数据，传入时间维度参数
    getInviteTimeStats(params, chartTimeDimension.value).then(response => {
      if (response.code === 200 && response.data) {
        const chartData = response.data;

        // 提取时间标签和数据
        const timeLabels = chartData.map(item => item.timeLabel);
        const visitCounts = chartData.map(item => item.visitCount || 0);
        const registrationCounts = chartData.map(item => item.registrationCount || 0);

        // 计算转化率
        const conversionRates = chartData.map(item => {
          const visits = item.visitCount || 0;
          const registrations = item.registrationCount || 0;
          return visits > 0 ? ((registrations / visits) * 100).toFixed(2) : '0.00';
        });

        // 获取时间维度显示文本
        const timeDimensionText = {
          'daily': '按日统计',
          'weekly': '按周统计',
          'monthly': '按月统计'
        };

        // 配置图表选项
        const option = {
          title: {
            text: `邀请统计图表 - ${timeDimensionText[chartTimeDimension.value]}`,
            left: 'center',
            textStyle: {
              fontSize: 18,
              fontWeight: 'bold',
              color: '#333'
            },
            subtext: chartInviteCode.value ? `邀请码: ${chartInviteCode.value}` : '全部邀请码',
            subtextStyle: {
              color: '#666',
              fontSize: 12
            }
          },
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'shadow',
              shadowStyle: {
                color: 'rgba(0,0,0,0.1)'
              }
            },
            backgroundColor: 'rgba(255, 255, 255, 0.95)',
            borderColor: '#ccc',
            borderWidth: 1,
            textStyle: {
              color: '#333'
            },
            formatter: function(params) {
              let result = `<div style="padding: 8px;">`;
              result += `<div style="font-weight: bold; margin-bottom: 8px; color: #333;">${params[0].axisValue}</div>`;

              params.forEach((param, index) => {
                const color = param.color;
                const seriesName = param.seriesName;
                const value = param.value;

                result += `<div style="margin-bottom: 4px;">`;
                result += `<span style="display: inline-block; width: 10px; height: 10px; background-color: ${color}; border-radius: 50%; margin-right: 8px;"></span>`;
                result += `<span style="font-weight: 500;">${seriesName}:</span> `;
                result += `<span style="color: ${color}; font-weight: bold;">${value}</span>`;
                result += `</div>`;
              });

              // 添加转化率信息
              const dataIndex = params[0].dataIndex;
              const conversionRate = conversionRates[dataIndex];
              result += `<div style="margin-top: 8px; padding-top: 8px; border-top: 1px solid #eee;">`;
              result += `<span style="font-weight: 500;">转化率:</span> `;
              result += `<span style="color: #E6A23C; font-weight: bold;">${conversionRate}%</span>`;
              result += `</div>`;

              result += `</div>`;
              return result;
            }
          },
          legend: {
            data: ['浏览量', '注册量'],
            top: 50,
            itemGap: 20,
            textStyle: {
              fontSize: 12,
              color: '#666'
            }
          },
          grid: {
            left: '5%',
            right: '5%',
            bottom: '15%',
            top: '20%',
            containLabel: true
          },
          xAxis: {
            type: 'category',
            data: timeLabels,
            axisLabel: {
              rotate: 45,
              fontSize: 11,
              color: '#666',
              margin: 10
            },
            axisLine: {
              lineStyle: {
                color: '#e0e0e0'
              }
            },
            axisTick: {
              alignWithLabel: true
            }
          },
          yAxis: {
            type: 'value',
            name: '数量',
            nameTextStyle: {
              color: '#666',
              fontSize: 12
            },
            axisLabel: {
              fontSize: 11,
              color: '#666'
            },
            axisLine: {
              lineStyle: {
                color: '#e0e0e0'
              }
            },
            splitLine: {
              lineStyle: {
                color: '#f0f0f0',
                type: 'dashed'
              }
            }
          },
          series: [
            {
              name: '浏览量',
              type: 'bar',
              data: visitCounts,
              barWidth: '8%',
              barGap: '50%',
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#409EFF' },
                  { offset: 1, color: '#79bbff' }
                ]),
                borderRadius: [3, 3, 0, 0]
              },
              emphasis: {
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#337ecc' },
                    { offset: 1, color: '#6bb2ff' }
                  ])
                }
              },
              label: {
                show: false,
                position: 'top',
                fontSize: 10,
                color: '#666'
              }
            },
            {
              name: '注册量',
              type: 'bar',
              data: registrationCounts,
              barWidth: '8%',
              itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  { offset: 0, color: '#67C23A' },
                  { offset: 1, color: '#95d475' }
                ]),
                borderRadius: [3, 3, 0, 0]
              },
              emphasis: {
                itemStyle: {
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#529b2e' },
                    { offset: 1, color: '#85ce61' }
                  ])
                }
              },
              label: {
                show: false,
                position: 'top',
                fontSize: 10,
                color: '#666'
              }
            }
          ],
          // 添加数据缩放功能
          dataZoom: [
            {
              type: 'slider',
              show: timeLabels.length > 10,
              xAxisIndex: 0,
              bottom: '5%',
              height: 20,
              start: 0,
              end: 100
            }
          ],
          // 添加工具箱
          toolbox: {
            show: true,
            right: '2%',
            top: '2%',
            feature: {
              saveAsImage: {
                title: '保存为图片',
                name: '邀请统计图表'
              },
              dataView: {
                title: '数据视图',
                readOnly: true
              },
              magicType: {
                title: {
                  line: '切换为折线图',
                  bar: '切换为柱状图'
                },
                type: ['line', 'bar']
              },
              restore: {
                title: '还原'
              }
            }
          }
        };

        chartInstance.value.setOption(option, true);
      }
    }).catch(error => {
      console.error('获取图表数据失败:', error);
      proxy.$modal.msgError("获取图表数据失败");
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    inviteCode: null,
                    codeName: null,
                    totalVisits: null,
                    totalRegistrations: null,
                    status: null,
                    createdAt: null
    };
    proxy.resetForm("codesRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加邀请码";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getCodes(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改邀请码";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["codesRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateCodes(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addCodes(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除邀请码编号为"' + _ids + '"的数据项？').then(function() {
      return delCodes(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/codes/export', {
      ...queryParams.value
    }, `codes_${new Date().getTime()}.xlsx`)
  }

  // 窗口大小变化时重新调整图表
  const handleResize = () => {
    if (chartInstance.value) {
      chartInstance.value.resize();
    }
  };

  // 组件挂载后初始化图表
  onMounted(() => {
    nextTick(() => {
      initChart();
      // 监听窗口大小变化
      window.addEventListener('resize', handleResize);
    });
  });

  // 组件卸载前销毁图表
  onUnmounted(() => {
    if (chartInstance.value) {
      chartInstance.value.dispose();
    }
    // 移除窗口大小变化监听
    window.removeEventListener('resize', handleResize);
  });

  getList();
</script>

<style scoped>
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-controls {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  gap: 10px;
}

.mb8 {
  margin-bottom: 8px;
}
</style>
