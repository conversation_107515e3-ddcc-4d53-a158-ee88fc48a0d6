<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="标题" prop="title">
                    <el-input
                        v-model="queryParams.title"
                        placeholder="请输入标题"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="用户 ID" prop="userId">
                    <el-input
                        v-model="queryParams.userId"
                        placeholder="请输入用户 ID"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:posts:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:posts:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:posts:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:posts:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Picture"
            @click="handleSetCarousel"
            v-hasPermi="['system:posts:edit']"
        >设置帖子轮播图</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="postsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="帖子 ID" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="标题" align="center" prop="title" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="内容" align="center" prop="content" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户 ID" align="center" prop="userId" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="点赞数" align="center" prop="likes" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="收藏数" align="center" prop="favorites" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="浏览量" align="center" prop="views" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="评论数" align="center" prop="commentCount" v-if="columns[7].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="标签" align="center" v-if="columns[8].visible" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <el-tag v-for="tag in scope.row.tags" :key="tag.id" class="ml-2" style="margin-right: 4px">
                      {{ tag.name }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="图片" align="center" width="150" v-if="columns[9].visible">
                <template #default="scope">
                  <div style="display: flex; gap: 4px; justify-content: center">
                    <template v-for="(url, index) in scope.row.imageUrls" :key="index">
                      <!-- 图片处理 -->
                      <image-preview v-if="isImageFile(url)" :src="url" :width="40" :height="40"/>
                      <!-- 视频处理 -->
                      <div v-else-if="isVideoFile(url)" class="video-thumbnail" style="width: 40px; height: 40px; position: relative;">
                        <el-icon class="video-icon"><video-play /></el-icon>
                      </div>
                    </template>
                  </div>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="View" @click="handleDetail(scope.row)" v-hasPermi="['system:posts:query']">详情</el-button>
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:posts:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:posts:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 详情对话框 -->
    <el-dialog title="帖子详情" v-model="detailOpen" width="800px" append-to-body>
      <div v-if="detailForm" class="post-detail">
        <h2>{{ detailForm.title }}</h2>
        <div class="post-meta">
          <el-descriptions :column="3" border>
            <el-descriptions-item label="帖子ID">{{ detailForm.id }}</el-descriptions-item>
            <el-descriptions-item label="用户ID">{{ detailForm.userId }}</el-descriptions-item>
            <el-descriptions-item label="浏览量">{{ detailForm.views }}</el-descriptions-item>
            <el-descriptions-item label="点赞数">{{ detailForm.likes }}</el-descriptions-item>
            <el-descriptions-item label="收藏数">{{ detailForm.favorites }}</el-descriptions-item>
            <el-descriptions-item label="评论数">{{ detailForm.commentCount }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ detailForm.createTime }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ detailForm.updateTime }}</el-descriptions-item>
          </el-descriptions>
        </div>
        
        <div class="post-tags" v-if="detailForm.tags && detailForm.tags.length">
          <h3>标签</h3>
          <div>
            <el-tag v-for="tag in detailForm.tags" :key="tag.id" class="ml-2" style="margin-right: 8px; margin-bottom: 8px;">
              {{ tag.name }}
            </el-tag>
          </div>
        </div>

        <div class="post-images" v-if="detailForm.imageUrls && detailForm.imageUrls.length">
          <h3>媒体文件</h3>
          <div class="media-container">
            <!-- 图片预览 -->
            <el-image 
              v-for="(url, index) in detailForm.imageUrls.filter(url => isImageFile(url))" 
              :key="`img-${index}`" 
              :src="url" 
              :preview-src-list="detailForm.imageUrls.filter(url => isImageFile(url))"
              :initial-index="index"
              fit="cover"
              style="width: 150px; height: 150px; margin-right: 10px; margin-bottom: 10px;"
            />
            
            <!-- 视频预览 -->
            <div v-for="(url, index) in detailForm.imageUrls.filter(url => isVideoFile(url))" 
                :key="`video-${index}`"
                class="video-preview"
                style="width: 150px; height: 150px; margin-right: 10px; margin-bottom: 10px;">
              <video 
                :src="url" 
                controls 
                style="width: 100%; height: 100%; object-fit: cover;"
              ></video>
            </div>
          </div>
        </div>

        <div class="post-content">
          <h3>内容</h3>
          <div v-html="detailForm.content" class="content-html"></div>
        </div>

        <div class="post-comments" v-if="detailForm.comments && detailForm.comments.length">
          <h3>评论 ({{ detailForm.commentCount }})</h3>
          <div class="comment-list">
            <div v-for="comment in detailForm.comments" :key="comment.id" class="comment-item">
              <div class="comment-header">
                <el-avatar :size="40" :src="comment.userAvatar"></el-avatar>
                <div class="comment-info">
                  <div class="comment-username">{{ comment.username }}</div>
                  <div class="comment-time">{{ comment.createTime }}</div>
                </div>
              </div>
              <div class="comment-content">{{ comment.content }}</div>
              
              <!-- 子评论 -->
              <div v-if="comment.children && comment.children.length" class="comment-replies">
                <div v-for="reply in comment.children" :key="reply.id" class="comment-reply-item">
                  <div class="comment-header">
                    <el-avatar :size="30" :src="reply.userAvatar"></el-avatar>
                    <div class="comment-info">
                      <div class="comment-username">{{ reply.username }}</div>
                      <div class="comment-time">{{ reply.createTime }}</div>
                    </div>
                  </div>
                  <div class="comment-content">{{ reply.content }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 添加或修改帖子管理对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="postsRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="标题" prop="title">
                          <el-input v-model="form.title" placeholder="请输入标题" />
                        </el-form-item>
                        <el-form-item label="内容">
                          <editor v-model="form.content" :min-height="192"/>
                        </el-form-item>
                        <el-form-item label="用户 ID" prop="userId">
                          <el-input v-model="form.userId" placeholder="请输入用户 ID" />
                        </el-form-item>
                        <el-form-item label="点赞数" prop="likes">
                          <el-input v-model="form.likes" placeholder="请输入点赞数" />
                        </el-form-item>
                        <el-form-item label="收藏数" prop="favorites">
                          <el-input v-model="form.favorites" placeholder="请输入收藏数" />
                        </el-form-item>
                        <el-form-item label="浏览量" prop="views">
                          <el-input v-model="form.views" placeholder="请输入浏览量" />
                        </el-form-item>
                        <el-form-item label="媒体文件" prop="imageUrls">
                          <media-upload v-model="form.imageUrls" :limit="5" multiple />
                          <div class="upload-hint" style="margin-top: 8px; color: #909399; font-size: 12px;">
                            支持上传图片(jpg, png, gif)或视频(mp4, webm)文件
                          </div>
                        </el-form-item>
                        <el-form-item label="标签" prop="tags">
                          <div class="tag-display">
                            <el-tag v-for="tag in form.tags" :key="tag.id" 
                              closable @close="removeTag(tag)" 
                              class="ml-2" style="margin-right: 4px; margin-bottom: 4px;">
                              {{ tag.name }}
                            </el-tag>
                            
                            <el-select
                              v-if="tagInputVisible"
                              ref="tagSelectRef"
                              v-model="tagInputValue"
                              class="tag-input"
                              @change="handleTagSelect"
                              filterable
                              placeholder="请选择标签"
                              style="width: 150px">
                              <el-option
                                v-for="tag in tagsList"
                                :key="tag.id"
                                :label="tag.name"
                                :value="tag.id"
                                :disabled="form.tags.some(t => t.id === tag.id)">
                              </el-option>
                            </el-select>
                            
                            <el-button v-else class="button-new-tag" size="small" @click="showTagInput">
                              + 添加标签
                            </el-button>
                          </div>
                        </el-form-item>
                        
                        <!-- 评论展示区域 -->
                        <el-form-item label="评论" v-if="form.comments && form.comments.length">
                          <div class="comment-section">
                            <div class="comments-count">共 {{ form.commentCount || form.comments.length }} 条评论</div>
                            <el-alert
                              title="评论不可在后台直接修改，请通过前台用户界面进行互动"
                              type="info"
                              :closable="false"
                              show-icon>
                            </el-alert>
                            <el-collapse>
                              <el-collapse-item title="查看评论" name="1">
                                <div class="comment-list">
                                  <div v-for="comment in form.comments" :key="comment.id" class="comment-item">
                                    <div class="comment-header">
                                      <el-avatar :size="32" :src="comment.userAvatar"></el-avatar>
                                      <div class="comment-info">
                                        <div class="comment-username">{{ comment.username }}</div>
                                        <div class="comment-time">{{ comment.createTime }}</div>
                                      </div>
                                    </div>
                                    <div class="comment-content">{{ comment.content }}</div>
                                    
                                    <!-- 子评论 -->
                                    <div v-if="comment.children && comment.children.length" class="comment-replies">
                                      <div v-for="reply in comment.children" :key="reply.id" class="comment-reply-item">
                                        <div class="comment-header">
                                          <el-avatar :size="24" :src="reply.userAvatar"></el-avatar>
                                          <div class="comment-info">
                                            <div class="comment-username">{{ reply.username }}</div>
                                            <div class="comment-time">{{ reply.createTime }}</div>
                                          </div>
                                        </div>
                                        <div class="comment-content">{{ reply.content }}</div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </el-collapse-item>
                            </el-collapse>
                          </div>
                        </el-form-item>
                        <el-form-item label="评论" v-else>
                          <div class="no-comments">
                            <span style="color: #999;">暂无评论</span>
                          </div>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 设置帖子轮播图对话框 -->
    <el-dialog title="设置帖子轮播图" v-model="carouselOpen" width="800px" append-to-body>
      <div class="carousel-container">
        <el-alert
          title="请选择需要设置为轮播图的帖子，并设置播放顺序（数字越小优先显示）"
          type="info"
          :closable="false"
          show-icon
          style="margin-bottom: 15px;"
        />
        <el-table :data="allPostsList" @selection-change="handleCarouselSelectionChange" height="400px">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column label="帖子ID" align="center" prop="id" width="80" />
          <el-table-column label="标题" align="left" prop="title" show-overflow-tooltip />
          <el-table-column label="媒体预览" align="center" width="140">
            <template #default="scope">
              <template v-if="scope.row.imageUrls && scope.row.imageUrls.length > 0">
                <!-- 显示图片 -->
                <el-image
                  v-if="isImageFile(scope.row.imageUrls[0])"
                  :src="scope.row.imageUrls[0]"
                  style="width: 90px; height: 60px; border-radius: 4px;"
                  fit="cover"
                  :preview-src-list="[scope.row.imageUrls[0]]"
                />
                <!-- 显示视频缩略图 -->
                <div v-else-if="isVideoFile(scope.row.imageUrls[0])"
                  class="video-thumbnail"
                  style="width: 90px; height: 60px; display: flex; align-items: center; justify-content: center; background: #f5f7fa; border: 1px solid #e4e7ed; border-radius: 4px; cursor: pointer;"
                  @click="previewVideo(scope.row.imageUrls[0])">
                  <div style="text-align: center;">
                    <el-icon style="font-size: 20px; color: #409EFF; margin-bottom: 2px;"><video-play /></el-icon>
                    <div style="font-size: 10px; color: #909399;">点击播放</div>
                  </div>
                </div>
                <!-- 显示多媒体数量 -->
                <div v-if="scope.row.imageUrls.length > 1"
                  style="font-size: 10px; color: #909399; margin-top: 2px;">
                  +{{ scope.row.imageUrls.length - 1 }}个文件
                </div>
              </template>
              <span v-else style="color: #c0c4cc; font-size: 12px;">无媒体文件</span>
            </template>
          </el-table-column>
          <el-table-column label="播放顺序" align="center" width="140">
            <template #default="scope">
              <el-input-number
                v-model="scope.row.order"
                :min="1"
                :max="100"
                size="small"
                style="width: 100px;"
                :controls-position="'right'"
                @change="(value) => handleOrderChange(scope.row, value)"
              />
            </template>
          </el-table-column>
          <el-table-column label="当前状态" align="center" width="100">
            <template #default="scope">
              <el-tag type="success" v-if="isCarouselPost(scope.row.id)">已设为轮播</el-tag>
              <el-tag type="info" v-else>普通帖子</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="100">
            <template #default="scope">
              <el-button 
                v-if="isCarouselPost(scope.row.id)" 
                link type="danger" 
                icon="Delete" 
                @click="removeFromCarousel(scope.row)"
              >移除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitCarouselSettings">确 定</el-button>
          <el-button @click="carouselOpen = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 视频预览弹出层 -->
    <el-dialog
      title="视频预览"
      v-model="videoPreviewOpen"
      width="800px"
      append-to-body
      :close-on-click-modal="true"
      @close="closeVideoPreview">
      <div class="video-preview-container">
        <video
          v-if="currentVideoUrl"
          :src="currentVideoUrl"
          controls
          autoplay
          style="width: 100%; max-height: 500px; border-radius: 8px;"
          @loadstart="$event.target.volume = 0.5">
          您的浏览器不支持视频播放。
        </video>
        <div v-else class="video-error">
          <el-icon style="font-size: 48px; color: #c0c4cc;"><video-play /></el-icon>
          <p style="color: #909399; margin-top: 10px;">视频加载失败</p>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="closeVideoPreview">关闭</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup name="Posts">
  import { listPosts, getPosts, delPosts, addPosts, updatePosts, getPostDetail, updatePostDetail } from "@/api/system/posts";
  // import { listAgtTags } from "@/api/system/AgtTags";
  import { listAgtTags } from "../../../api/system/AgtTags";
  import { listOmgHomePostBanner, addOmgHomePostBanner, delOmgHomePostBanner } from "@/api/system/OmgHomePostBanner";
  import { VideoPlay } from '@element-plus/icons-vue';

  const { proxy } = getCurrentInstance();

  const postsList = ref([]);
  const open = ref(false);
  const detailOpen = ref(false);
  const detailForm = ref(null);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  const tagsList = ref([]); // 标签列表
  const tagInputVisible = ref(false);
  const tagInputValue = ref('');
  const tagSelectRef = ref(null);
  
  // 轮播图相关变量
  const carouselOpen = ref(false);
  const allPostsList = ref([]);
  const selectedCarouselPosts = ref([]);
  const carouselPostIds = ref([]); // 存储当前已设为轮播图的帖子ID
  const carouselItems = ref([]); // 存储完整的轮播图项数据，包含ID

  // 视频预览相关变量
  const videoPreviewOpen = ref(false);
  const currentVideoUrl = ref('');

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    title: null,
                    content: null,
                    userId: null
    },
    rules: {
    },
    //表格展示列
    columns: [
              { key: 0, label: '帖子 ID', visible: true },
                { key: 1, label: '标题', visible: true },
                { key: 2, label: '内容', visible: true },
                { key: 3, label: '用户 ID', visible: true },
                { key: 4, label: '点赞数', visible: true },
                { key: 5, label: '收藏数', visible: true },
                { key: 6, label: '浏览量', visible: true },
                { key: 7, label: '评论数', visible: true },
                { key: 8, label: '标签', visible: true },
                { key: 9, label: '图片', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询帖子管理列表 */
  function getList() {
    loading.value = true;
    listPosts(queryParams.value).then(response => {
            postsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    title: null,
                    content: null,
                    userId: null,
                    likes: null,
                    favorites: null,
                    views: null,
                    createTime: null,
                    updateTime: null,
                    imageUrls: [],
                    tags: []
    };
    proxy.resetForm("postsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 获取标签列表 */
  function getTagsList() {
    // 设置pageSize为很大的值，确保获取所有标签
    const queryParams = {
      pageNum: 1,
      pageSize: 1000  // 设置一个足够大的值以获取所有标签
    };
    listAgtTags(queryParams).then(response => {
      tagsList.value = response.rows;
    });
  }
  
  // 打开标签输入框
  function showTagInput() {
    tagInputVisible.value = true;
    nextTick(() => {
      tagSelectRef.value.focus();
    });
  }

  // 选择标签
  function handleTagSelect(tagId) {
    if (tagId && !form.value.tags.some(tag => tag.id === tagId)) {
      const selectedTag = tagsList.value.find(tag => tag.id === tagId);
      if (selectedTag) {
        form.value.tags.push({
          id: selectedTag.id,
          name: selectedTag.name
        });
      }
    }
    tagInputVisible.value = false;
    tagInputValue.value = '';
  }
  
  // 移除标签
  function removeTag(tag) {
    if (form.value.tags) {
      form.value.tags = form.value.tags.filter(item => item.id !== tag.id);
    }
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    getTagsList(); // 获取标签列表
    open.value = true;
    title.value = "添加帖子管理";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getTagsList(); // 获取标签列表
    getPostDetail(_id).then(response => {
      form.value = response.data;
      // 处理返回的数据以适应表单
      if (form.value) {
        // 确保保留必要的字段，如果不存在则设置默认值
        form.value.likes = form.value.likes || '0';
        form.value.favorites = form.value.favorites || '0';
        form.value.views = form.value.views || '0';
        form.value.imageUrls = form.value.imageUrls || [];
        form.value.tags = form.value.tags || [];
      }
      open.value = true;
      title.value = "修改帖子管理";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["postsRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          // 准备提交的数据，转换为后端需要的格式
          const postDetailEdit = {
            id: form.value.id,
            title: form.value.title,
            content: form.value.content,
            userId: form.value.userId,
            likes: form.value.likes,
            favorites: form.value.favorites,
            views: form.value.views,
            // 确保imageUrls是数组格式
            imageUrls: Array.isArray(form.value.imageUrls) ? form.value.imageUrls : 
                      (typeof form.value.imageUrls === 'string' ? form.value.imageUrls.split(',').filter(url => url) : []),
            tagIds: form.value.tags ? form.value.tags.map(tag => tag.id) : []
          };
          
          updatePostDetail(postDetailEdit).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          // 新增帖子时，确保包含图片路径
          const postData = {
            ...form.value,
            // 确保imageUrls是数组格式
            imageUrls: Array.isArray(form.value.imageUrls) ? form.value.imageUrls : 
                      (typeof form.value.imageUrls === 'string' ? form.value.imageUrls.split(',').filter(url => url) : [])
          };
          
          addPosts(postData).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除帖子管理编号为"' + _ids + '"的数据项？').then(function() {
      return delPosts(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/posts/export', {
      ...queryParams.value
    }, `posts_${new Date().getTime()}.xlsx`)
  }

  /** 查看详情按钮操作 */
  function handleDetail(row) {
    const _id = row.id;
    getPostDetail(_id).then(response => {
      detailForm.value = response.data;
      detailOpen.value = true;
    });
  }

  /** 设置帖子轮播图按钮操作 */
  function handleSetCarousel() {
    carouselOpen.value = true;
    getAllPosts();
  }
  
  /** 获取所有帖子列表（用于轮播图设置） */
  function getAllPosts() {
    loading.value = true;
    // 获取所有帖子，pageSize设置较大值
    listPosts({
      pageNum: 1,
      pageSize: 100
    }).then(response => {
      // 初始化每个帖子的order属性
      allPostsList.value = response.rows.map(post => ({
        ...post,
        order: 1 // 默认顺序值
      }));
      loading.value = false;
      // 获取已有轮播图设置
      getCarouselPosts();
    });
  }
  
  /** 获取当前已设置为轮播图的帖子 */
  function getCarouselPosts() {
    // 调用获取轮播图设置的API
    listOmgHomePostBanner().then(response => {
      if (response.code === 200 && response.rows) {
        const carouselData = response.rows;
        carouselPostIds.value = carouselData.map(item => parseInt(item.postId));
        
        // 存储轮播图数据，包含ID，用于后续删除操作
        carouselItems.value = carouselData;
        
        // 更新帖子的顺序值
        carouselData.forEach(item => {
          const post = allPostsList.value.find(p => parseInt(p.id) === parseInt(item.postId));
          if (post) {
            post.order = item.sortOrder || 1;
          }
        });
        
        // 预选已设为轮播图的帖子
        nextTick(() => {
          selectedCarouselPosts.value = allPostsList.value.filter(post => 
            carouselPostIds.value.includes(parseInt(post.id))
          );
        });
      }
    });
  }
  
  /** 检查帖子是否已设为轮播图 */
  function isCarouselPost(id) {
    return carouselPostIds.value.includes(parseInt(id));
  }
  
  /** 轮播图选择变更 */
  function handleCarouselSelectionChange(selection) {
    selectedCarouselPosts.value = selection;
  }
  
  /** 处理顺序变更 */
  function handleOrderChange(post, value) {
    post.order = value;
  }
  
  /** 提交轮播图设置 */
  function submitCarouselSettings() {
    if (selectedCarouselPosts.value.length === 0) {
      proxy.$modal.msgError('请至少选择一个帖子作为轮播图');
      return;
    }
    
    // 显示确认对话框
    proxy.$modal.confirm('此操作将更新轮播图设置，之前的设置将被覆盖，是否继续？').then(function() {
      // 清除现有轮播图设置
      clearExistingCarouselItems().then(() => {
        // 创建批量提交的请求数组
        const promises = selectedCarouselPosts.value.map(post => {
          // 为每个选中的帖子创建一个单独的请求
          const postData = {
            postId: post.id,
            sortOrder: post.order || 1
          };
          
          return addOmgHomePostBanner(postData);
        });
        
        // 使用Promise.all等待所有请求完成
        Promise.all(promises)
          .then(responses => {
            // 检查是否所有请求都成功
            const allSuccess = responses.every(response => response.code === 200);
            if (allSuccess) {
              proxy.$modal.msgSuccess('轮播图设置成功');
              carouselOpen.value = false;
            } else {
              proxy.$modal.msgWarning('部分轮播图设置失败');
            }
          })
          .catch(error => {
            console.error('设置轮播图出错:', error);
            proxy.$modal.msgError('设置轮播图失败: ' + (error.message || '未知错误'));
          });
      }).catch(error => {
        console.error('清除轮播图设置出错:', error);
        proxy.$modal.msgError('清除现有轮播图设置失败');
      });
    }).catch(() => {});
  }
  
  /** 清除现有的所有轮播图设置 */
  function clearExistingCarouselItems() {
    // 如果没有现有轮播图，直接返回成功
    if (carouselItems.value.length === 0) {
      return Promise.resolve();
    }
    
    // 创建删除请求
    const deletePromises = carouselItems.value.map(item => {
      // 如果item有id属性，使用它删除
      if (item.id) {
        return delOmgHomePostBanner(item.id);
      }
      return Promise.resolve(); // 无ID的项直接跳过
    });
    
    return Promise.all(deletePromises);
  }
  
  /** 从轮播图中删除单个帖子 */
  function removeFromCarousel(post) {
    // 从选中列表中移除
    selectedCarouselPosts.value = selectedCarouselPosts.value.filter(p => p.id !== post.id);
    
    // 如果有对应的现有轮播图项，也从carouselItems中移除
    const carouselItem = carouselItems.value.find(item => parseInt(item.postId) === parseInt(post.id));
    if (carouselItem && carouselItem.id) {
      proxy.$modal.confirm(`确定要从轮播图中移除"${post.title}"吗？`).then(() => {
        delOmgHomePostBanner(carouselItem.id).then(() => {
          proxy.$modal.msgSuccess('已从轮播图中移除');
          // 重新加载轮播图数据
          getCarouselPosts();
        }).catch(error => {
          console.error('移除轮播图项失败:', error);
          proxy.$modal.msgError('移除轮播图项失败');
        });
      }).catch(() => {});
    }
  }

  /** 判断是否为图片文件 */
  function isImageFile(url) {
    if (!url) return false;
    const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp'];
    return imageExtensions.some(ext => url.toLowerCase().endsWith(ext));
  }
  
  /** 判断是否为视频文件 */
  function isVideoFile(url) {
    if (!url) return false;
    const videoExtensions = ['.mp4', '.webm', '.ogg', '.mov', '.avi'];
    return videoExtensions.some(ext => url.toLowerCase().endsWith(ext));
  }

  /** 预览视频 */
  function previewVideo(videoUrl) {
    currentVideoUrl.value = videoUrl;
    videoPreviewOpen.value = true;
  }

  /** 关闭视频预览 */
  function closeVideoPreview() {
    videoPreviewOpen.value = false;
    currentVideoUrl.value = '';
  }

  getList();
</script>

<style scoped>
.post-detail {
  padding: 10px;
}

.post-detail h2 {
  margin-bottom: 15px;
  font-size: 22px;
}

.post-detail h3 {
  margin-top: 20px;
  margin-bottom: 10px;
  font-size: 16px;
  font-weight: 600;
  border-left: 3px solid #409EFF;
  padding-left: 10px;
}

.post-meta {
  margin-bottom: 20px;
}

.post-tags, .post-images, .post-content, .post-comments {
  margin-bottom: 25px;
}

.content-html {
  background: #f9f9f9;
  padding: 15px;
  border-radius: 4px;
  line-height: 1.6;
}

.comment-item {
  padding: 15px;
  border-bottom: 1px solid #eee;
  margin-bottom: 10px;
}

.comment-header {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
}

.comment-info {
  margin-left: 10px;
}

.comment-username {
  font-weight: bold;
}

.comment-time {
  font-size: 12px;
  color: #999;
}

.comment-content {
  padding-left: 50px;
  margin-bottom: 10px;
}

.comment-replies {
  padding-left: 50px;
  margin-top: 15px;
}

.comment-reply-item {
  background: #f9f9f9;
  padding: 10px;
  border-radius: 4px;
  margin-bottom: 10px;
}

.comment-reply-item .comment-content {
  padding-left: 40px;
}

.comment-section {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  background-color: #fafafa;
  padding: 10px;
}

.comments-count {
  margin-bottom: 10px;
  font-weight: bold;
  color: #606266;
}

.no-comments, .no-tags {
  padding: 10px;
  background-color: #fafafa;
  border-radius: 4px;
  text-align: center;
}

/* 添加一些表单特有的样式 */
:deep(.el-collapse-item__header) {
  font-weight: 500;
  color: #409EFF;
}

:deep(.el-collapse-item__content) {
  padding: 10px;
}

/* 确保表单在大对话框中有更好的布局 */
:deep(.el-form-item__content) {
  width: 100%;
}

/* 表单中的评论样式微调 */
.el-form .comment-item {
  padding: 10px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0 1px 2px rgba(0,0,0,0.05);
}

.el-form .comment-content {
  padding-left: 42px;
  line-height: 1.5;
  margin-bottom: 5px;
}

.el-form .comment-replies {
  padding-left: 42px;
  margin-top: 8px;
}

.el-form .comment-reply-item {
  background: #f5f7fa;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 5px;
}

.el-form .comment-reply-item .comment-content {
  padding-left: 34px;
}

.tag-display {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.button-new-tag {
  margin-left: 10px;
  height: 32px;
  line-height: 30px;
  padding-top: 0;
  padding-bottom: 0;
}

.tag-input {
  width: 150px;
  margin-left: 10px;
  vertical-align: bottom;
}

/* 修复对话框最大高度问题，防止内容过多导致对话框太高 */
:deep(.el-dialog__body) {
  max-height: calc(80vh - 100px);
  overflow-y: auto;
  padding: 20px;
}

.carousel-container {
  margin-bottom: 20px;
}

.el-table {
  margin-bottom: 20px;
}

.dialog-footer {
  text-align: right;
}

.post-images, .post-videos {
  margin-bottom: 25px;
}

.media-container {
  display: flex;
  flex-wrap: wrap;
}

.video-preview {
  position: relative;
  overflow: hidden;
  border-radius: 4px;
}

.video-thumbnail {
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f0f0f0;
  border-radius: 4px;
}

.video-icon {
  position: absolute;
  font-size: 20px;
  color: #409EFF;
}

/* 轮播图设置对话框样式 */
.carousel-container {
  max-height: 500px;
  overflow-y: auto;
}

.carousel-container .el-table {
  font-size: 13px;
}

.carousel-container .el-table .el-table__cell {
  padding: 8px 0;
}

/* 媒体预览样式优化 */
.carousel-container .video-thumbnail:hover {
  background: #ecf5ff !important;
  border-color: #409EFF !important;
}

.carousel-container .video-thumbnail:hover .el-icon {
  color: #409EFF !important;
}

/* 输入数字框样式优化 */
.carousel-container .el-input-number {
  width: 100px;
}

.carousel-container .el-input-number .el-input__inner {
  text-align: center;
}

/* 视频预览容器样式 */
.video-preview-container {
  text-align: center;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
}

.video-preview-container video {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  background: #000;
}

.video-error {
  padding: 40px;
  text-align: center;
  color: #909399;
}

.video-error .el-icon {
  margin-bottom: 10px;
}
</style>
