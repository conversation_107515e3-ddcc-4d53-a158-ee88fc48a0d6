<template>
  <div class="login-container">
    <!-- 顶部标题栏 -->
    <div class="login-header">
      <div class="logo-container">
        <img src="@/assets/logo/logo.png" alt="Logo" class="logo-img" />
        <span class="logo-text">AGTFIND</span>
        <span class="logo-subtext">A G T F I N D</span>
      </div>
      <div class="welcome-text">欢迎登录</div>
    </div>

    <!-- 登录内容区 -->
    <div class="login-content">
      <div class="login-box">
        <!-- 左侧登录表单 -->
        <div class="login-form-container">
          <el-form ref="loginRef" :model="loginForm" :rules="loginRules" class="login-form">
            <div class="input-group">
              <div class="input-label">账号</div>
              <el-form-item prop="username">
                <el-input
                  v-model="loginForm.username"
                  type="text"
                  size="large"
                  auto-complete="off"
                  placeholder="账号"
                />
              </el-form-item>
            </div>
            
            <div class="input-group">
              <div class="input-label">密码</div>
              <el-form-item prop="password">
                <el-input
                  v-model="loginForm.password"
                  type="password"
                  size="large"
                  auto-complete="off"
                  placeholder="密码"
                  @keyup.enter="handleLogin"
                />
              </el-form-item>
            </div>
            
            <div class="input-group" v-if="captchaEnabled">
              <div class="input-label">验证码</div>
              <el-form-item prop="code">
                <div class="captcha-container">
                  <el-input
                    v-model="loginForm.code"
                    size="large"
                    auto-complete="off"
                    placeholder="请输入"
                    @keyup.enter="handleLogin"
                  />
                  <div class="login-code">
                    <img :src="codeUrl" @click="getCode" class="login-code-img"/>
                  </div>
                </div>
              </el-form-item>
            </div>
            
            <el-checkbox v-model="loginForm.rememberMe" class="remember-me">记住密码</el-checkbox>
            
            <el-form-item class="login-button-container">
              <el-button
                :loading="loading"
                size="large"
                type="primary"
                class="login-button"
                @click.prevent="handleLogin"
              >
                <span v-if="!loading">登 录</span>
                <span v-else>登 录 中...</span>
              </el-button>
              <div class="register-link" v-if="register">
                <router-link class="link-type" :to="'/register'">立即注册</router-link>
              </div>
            </el-form-item>
          </el-form>
        </div>
        
        <!-- 右侧图形区域 -->
        <div class="login-image-container">
          <div class="image-content">
            <h2 class="image-title">欢迎使用AGT后台管理系统</h2>
            <p class="image-subtitle"></p>
            <div class="image-illustration">
              <!-- 这里使用插图，可以用图片替代 -->
              <img src="@/assets/login-illustration.svg" alt="Illustration" class="illustration-img" />
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 底部版权信息 -->
    <div class="login-footer">
      <span>Copyright © 2024-2025 AGT All Rights Reserved.</span>
    </div>
  </div>
</template>

<script setup>
import { getCodeImg } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import useUserStore from '@/store/modules/user'

const title = import.meta.env.VITE_APP_TITLE;
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();

const loginForm = ref({
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: ""
});

const loginRules = {
  username: [{ required: true, trigger: "blur", message: "请输入您的账号" }],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }]
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

watch(route, (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
}, { immediate: true });

function handleLogin() {
  proxy.$refs.loginRef.validate(valid => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), { expires: 30 });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      userStore.login(loginForm.value).then(() => {
        const query = route.query;
        const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
          if (cur !== "redirect") {
            acc[cur] = query[cur];
          }
          return acc;
        }, {});
        router.push({ path: redirect.value || "/", query: otherQueryParams });
      }).catch(() => {
        loading.value = false;
        // 重新获取验证码
        if (captchaEnabled.value) {
          getCode();
        }
      });
    }
  });
}

function getCode() {
  getCodeImg().then(res => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img;
      loginForm.value.uuid = res.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password: password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe)
  };
}

getCode();
getCookie();
</script>

<style lang='scss' scoped>
.login-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100%;
  background-color: #0E86FB;
  background-image: url('@/assets/login-bg.svg');
  background-size: cover;
  position: relative;
  overflow: hidden;
}

.login-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 40px;
  background-color: rgba(0, 58, 121, 0.8);
  color: #fff;
  height: 60px;
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-img {
  height: 36px;
  margin-right: 10px;
}

.logo-text {
  font-size: 24px;
  font-weight: bold;
}

.logo-subtext {
  font-size: 12px;
  margin-left: 8px;
  opacity: 0.8;
}

.welcome-text {
  font-size: 18px;
}

.login-content {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40px;
}

.login-box {
  display: flex;
  width: 980px;
  height: 500px;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.login-form-container {
  width: 400px;
  background-color: #1A1F2C;
  padding: 40px 30px;
}

.login-image-container {
  flex: 1;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-form {
  width: 100%;
}

.input-group {
  margin-bottom: 20px;
}

.input-label {
  color: #fff;
  font-size: 14px;
  margin-bottom: 8px;
}

.captcha-container {
  display: flex;
  align-items: center;
}

:deep(.el-input__wrapper) {
  border-radius: 4px;
  background-color: #13161F;
  border: 1px solid #2A3048;
  box-shadow: none;
  height: 42px;
}

:deep(.el-input__inner) {
  color: #fff;
  height: 40px;
}

:deep(.el-checkbox__inner) {
  background-color: #13161F;
  border-color: #2A3048;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  background-color: #0E86FB;
  border-color: #0E86FB;
}

.remember-me {
  color: #fff;
  margin-bottom: 30px;
}

.login-button-container {
  margin-bottom: 0;
}

.login-button {
  width: 100%;
  background-color: #0E86FB;
  border: none;
  height: 44px;
  font-size: 16px;
}

.login-code {
  margin-left: 10px;
}

.login-code-img {
  height: 42px;
  cursor: pointer;
}

.register-link {
  text-align: right;
  margin-top: 10px;
  
  .link-type {
    color: #0E86FB;
    text-decoration: none;
    font-size: 14px;
  }
}

.image-content {
  text-align: center;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100%;
}

.image-title {
  font-size: 24px;
  color: #333;
  margin-bottom: 30px;
}

.image-subtitle {
  font-size: 16px;
  color: #666;
  margin-bottom: 20px;
}

.illustration-img {
  max-width: 90%;
  max-height: 300px;
  width: auto;
  height: auto;
}

.login-footer {
  height: 40px;
  line-height: 40px;
  text-align: center;
  color: #fff;
  font-size: 12px;
  letter-spacing: 1px;
  background-color: rgba(0, 46, 97, 0.5);
}
</style>
