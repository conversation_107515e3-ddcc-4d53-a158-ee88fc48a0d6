<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="150px">
                  <el-form-item label="兑换品名称" prop="name">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入兑换品名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="库存" prop="stockQuantity">
                    <el-input
                        v-model="queryParams.stockQuantity"
                        placeholder="请输入库存"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="状态" prop="isActive" >
                    <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable style="width: 130px;">
                      <el-option key="true" label="上架" value="true" />
                      <el-option key="false" label="下架" value="false" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-input
                        v-model="queryParams.createdAt"
                        placeholder="请输入创建时间"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgGoods:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgGoods:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgGoods:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgGoods:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgGoodsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="兑换品名称" align="center" prop="name" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="兑换品描述" align="center" prop="description" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="兑换品图片" align="center" prop="imageUrl" width="100" v-if="columns[3].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.imageUrl" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="兑换该兑换品所需的积分数量" align="center" prop="pointRequired" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="库存" align="center" prop="stockQuantity" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="状态" align="center" prop="isActive" v-if="columns[6].visible">
                  <template #default="scope">
                    <el-tag type="success" v-if="scope.row.isActive === true || scope.row.isActive === 'true'">上架</el-tag>
                    <el-tag type="info" v-else>下架</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" prop="createdAt" v-if="columns[7].visible" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgGoods:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgGoods:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改积分商城对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgGoodsRef" :model="form" :rules="rules" label-width="100px">
                        <el-form-item label="兑换品名称" prop="name">
                          <el-input v-model="form.name" placeholder="请输入兑换品名称" />
                        </el-form-item>
                        <el-form-item label="兑换品描述" prop="description">
                          <el-input v-model="form.description" placeholder="请输入兑换品描述" />
                        </el-form-item>
                        <el-form-item label="兑换品图片" prop="imageUrl">
                          <image-upload v-model="form.imageUrl"/>
                        </el-form-item>
                        <el-form-item label="积分数量" prop="pointRequired">
                          <el-input v-model="form.pointRequired" placeholder="请输入兑换该兑换品所需的积分数量" />
                        </el-form-item>
                        <el-form-item label="库存" prop="stockQuantity">
                          <el-input v-model="form.stockQuantity" placeholder="请输入库存" />
                        </el-form-item>
                        <el-form-item label="状态" prop="isActive">
                          <el-select v-model="form.isActive" placeholder="请选择是否上架">
                            <el-option label="上架" :value="'true'" />
                            <el-option label="下架" :value="'false'" />
                          </el-select>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgGoods">
  import { listOmgGoods, getOmgGoods, delOmgGoods, addOmgGoods, updateOmgGoods } from "@/api/system/OmgGoods";

  const { proxy } = getCurrentInstance();

  const OmgGoodsList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    name: null,
                    stockQuantity: null,
                    isActive: null,
                    createdAt: null
    },
    rules: {
    },
    //表格展示列
    columns: [
              { key: 0, label: '积分商城唯一标识', visible: true },
                { key: 1, label: '兑换品名称', visible: true },
                { key: 2, label: '兑换品描述', visible: true },
                { key: 3, label: '兑换品图片', visible: true },
                { key: 4, label: '兑换该兑换品所需的积分数量', visible: true },
                { key: 5, label: '库存', visible: true },
                { key: 6, label: '表示兑换品是否上架。如果为 FALSE，则兑换品在用户端不可见', visible: true },
                { key: 7, label: '创建时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询积分商城列表 */
  function getList() {
    loading.value = true;
    listOmgGoods(queryParams.value).then(response => {
            OmgGoodsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    name: null,
                    description: null,
                    imageUrl: null,
                    pointRequired: null,
                    stockQuantity: null,
                    isActive: null,
                    createdAt: null
    };
    proxy.resetForm("OmgGoodsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加积分商城";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getOmgGoods(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改积分商城";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgGoodsRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateOmgGoods(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgGoods(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除积分商城编号为"' + _ids + '"的数据项？').then(function() {
      return delOmgGoods(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgGoods/export', {
      ...queryParams.value
    }, `OmgGoods_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
