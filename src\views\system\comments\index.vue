<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
      <el-form-item label="商品名称" prop="name">
        <el-input v-model="queryParams.name" placeholder="请输入关联商品ID" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <el-form-item label="用户名称" prop="username">
        <el-input v-model="queryParams.username" placeholder="请输入评论用户ID" clearable @keyup.enter="handleQuery" />
      </el-form-item>
      <!-- <el-form-item label="父评论ID" prop="parentCommentId">
        <el-input v-model="queryParams.parentCommentId" placeholder="请输入父评论ID" clearable @keyup.enter="handleQuery" />
      </el-form-item> -->
      <el-form-item label="评论创建时间" prop="createdAt">
        <el-date-picker clearable v-model="queryParams.createdAt" type="date" value-format="YYYY-MM-DD"
          placeholder="请选择评论创建时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" plain icon="Plus" @click="handleAdd"
          v-hasPermi="['system:comments:add']">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" plain icon="Edit" :disabled="single" @click="handleUpdate"
          v-hasPermi="['system:comments:edit']">修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
          v-hasPermi="['system:comments:remove']">删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="warning" plain icon="Download" @click="handleExport"
          v-hasPermi="['system:comments:export']">导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="commentsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="序号" align="center" prop="commentId" v-if="columns[0].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="关联商品ID" align="center" prop="name" v-if="columns[1].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="评论用户ID" align="center" prop="username" v-if="columns[2].visible"
        :show-overflow-tooltip="true" />
      <!-- <el-table-column label="父评论ID" align="center" prop="parentCommentId" v-if="columns[3].visible"
        :show-overflow-tooltip="true" /> -->
      <el-table-column label="评论内容" align="center" prop="content" v-if="columns[4].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="点赞数" align="center" prop="likes" v-if="columns[5].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="评论创建时间" align="center" prop="createdAt" width="180" v-if="columns[6].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评论最后更新时间" align="center" prop="updatedAt" width="180" v-if="columns[7].visible"
        :show-overflow-tooltip="true">
        <template #default="scope">
          <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="评论状态" align="center" prop="status" v-if="columns[8].visible"
        :show-overflow-tooltip="true" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
            v-hasPermi="['system:comments:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
            v-hasPermi="['system:comments:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
      v-model:limit="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改商品评论管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="commentsRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="关联商品" prop="productId">
          <el-select v-model="form.productId" placeholder="请选择关联商品" filterable clearable>
            <el-option v-for="item in productOptions" :key="item.productId" :label="item.name"
              :value="item.productId" />
          </el-select>
        </el-form-item>
        <el-form-item label="评论用户" prop="userId">
          <el-select v-model="form.userId" placeholder="请选择评论用户" filterable clearable>
            <el-option v-for="item in userOptions" :key="item.userId" :label="item.username" :value="item.userId" />
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="父评论ID" prop="parentCommentId">
          <el-input v-model="form.parentCommentId" placeholder="请输入父评论ID" />
        </el-form-item> -->
        <el-form-item label="评论内容">
          <editor v-model="form.content" :min-height="192" />
        </el-form-item>
        <el-form-item label="点赞数" prop="likes">
          <el-input v-model="form.likes" placeholder="请输入点赞数" />
        </el-form-item>
        <el-form-item label="评论创建时间" prop="createdAt">
          <el-date-picker clearable v-model="form.createdAt" type="date" value-format="YYYY-MM-DD"
            placeholder="请选择评论创建时间">
          </el-date-picker>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Comments">
import { listComments, getComments, delComments, addComments, updateComments } from "@/api/system/comments";
import { listProducts } from "@/api/system/products";
import { listUsers } from "@/api/system/users";

const { proxy } = getCurrentInstance();

const commentsList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

// 商品和用户选项
const productOptions = ref([]);
const userOptions = ref([]);

const data = reactive({
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    productId: null,
    userId: null,
    parentCommentId: null,
    content: null,
    createdAt: null,
  },
  rules: {
    productId: [
      { required: true, message: "关联商品不能为空", trigger: "change" }
    ],
    userId: [
      { required: true, message: "评论用户不能为空", trigger: "change" }
    ],
    content: [
      { required: true, message: "评论内容不能为空", trigger: "blur" }
    ],
  },
  //表格展示列
  columns: [
    { key: 0, label: '评论唯一标识', visible: true },
    { key: 1, label: '关联商品ID', visible: true },
    { key: 2, label: '评论用户ID', visible: true },
    { key: 3, label: '父评论ID', visible: true },
    { key: 4, label: '评论内容', visible: true },
    { key: 5, label: '点赞数', visible: true },
    { key: 6, label: '评论创建时间', visible: true },
    { key: 7, label: '评论最后更新时间', visible: true },
    { key: 8, label: '评论状态', visible: true },
  ],
});

const { queryParams, form, rules, columns } = toRefs(data);

/** 查询商品评论管理列表 */
function getList() {
  loading.value = true;
  listComments(queryParams.value).then(response => {
    commentsList.value = response.rows;
    total.value = response.total;
    loading.value = false;
  });
}

/** 获取所有商品列表 */
function getProductOptions() {
  listProducts({ pageSize: 1000 }).then(response => {
    productOptions.value = response.rows;
  });
}

/** 获取所有用户列表 */
function getUserOptions() {
  listUsers({ pageSize: 1000 }).then(response => {
    userOptions.value = response.rows;
  });
}

// 取消按钮
function cancel() {
  open.value = false;
  reset();
}

// 表单重置
function reset() {
  form.value = {
    commentId: null,
    productId: null,
    userId: null,
    parentCommentId: null,
    content: null,
    likes: null,
    createdAt: null,
    updatedAt: null,
    status: null
  };
  proxy.resetForm("commentsRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 多选框选中数据
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.commentId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加商品评论管理";
  // 获取商品和用户选项
  getProductOptions();
  getUserOptions();
}

/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const _commentId = row.commentId || ids.value
  getComments(_commentId).then(response => {
    form.value = response.data;
    open.value = true;
    title.value = "修改商品评论管理";
    // 获取商品和用户选项
    getProductOptions();
    getUserOptions();
  });
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["commentsRef"].validate(valid => {
    if (valid) {
      if (form.value.commentId != null) {
        updateComments(form.value).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          open.value = false;
          getList();
        });
      } else {
        addComments(form.value).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          open.value = false;
          getList();
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete(row) {
  const _commentIds = row.commentId || ids.value;
  proxy.$modal.confirm('是否确认删除商品评论管理编号为"' + _commentIds + '"的数据项？').then(function () {
    return delComments(_commentIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download('system/comments/export', {
    ...queryParams.value
  }, `comments_${new Date().getTime()}.xlsx`)
}

getList();
</script>
