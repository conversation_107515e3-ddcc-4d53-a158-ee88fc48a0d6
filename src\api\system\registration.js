import request from '@/utils/request'

// 查询邀请码注册记录列表
export function listRegistration(query) {
  return request({
    url: '/system/registration/list',
    method: 'get',
    params: query
  })
}

// 查询邀请码注册记录详细
export function getRegistration(id) {
  return request({
    url: '/system/registration/' + id,
    method: 'get'
  })
}

// 新增邀请码注册记录
export function addRegistration(data) {
  return request({
    url: '/system/registration',
    method: 'post',
    data: data
  })
}

// 修改邀请码注册记录
export function updateRegistration(data) {
  return request({
    url: '/system/registration',
    method: 'put',
    data: data
  })
}

// 删除邀请码注册记录
export function delRegistration(id) {
  return request({
    url: '/system/registration/' + id,
    method: 'delete'
  })
}
