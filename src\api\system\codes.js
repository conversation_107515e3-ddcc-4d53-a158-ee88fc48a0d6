import request from '@/utils/request'

// 查询邀请码列表
export function listCodes(query) {
  return request({
    url: '/system/codes/list',
    method: 'get',
    params: query
  })
}

// 查询邀请码详细
export function getCodes(id) {
  return request({
    url: '/system/codes/' + id,
    method: 'get'
  })
}

// 新增邀请码
export function addCodes(data) {
  return request({
    url: '/system/codes',
    method: 'post',
    data: data
  })
}

// 修改邀请码
export function updateCodes(data) {
  return request({
    url: '/system/codes',
    method: 'put',
    data: data
  })
}

// 删除邀请码
export function delCodes(id) {
  return request({
    url: '/system/codes/' + id,
    method: 'delete'
  })
}
