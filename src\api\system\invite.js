import request from '@/utils/request'

// 获取邀请统计数据 - 支持不同时间维度
export function getInviteTimeStats(params, timeDimension = 'daily') {
  return request({
    url: `/system/invite/time-stats/${timeDimension}`,
    method: 'get',
    params: params
  })
}

// 获取邀请统计数据 - 按日统计
export function getInviteTimeStatsDaily(params) {
  return request({
    url: '/system/invite/time-stats/daily',
    method: 'get',
    params: params
  })
}

// 获取邀请统计数据 - 按周统计
export function getInviteTimeStatsWeekly(params) {
  return request({
    url: '/system/invite/time-stats/weekly',
    method: 'get',
    params: params
  })
}

// 获取邀请统计数据 - 按月统计
export function getInviteTimeStatsMonthly(params) {
  return request({
    url: '/system/invite/time-stats/monthly',
    method: 'get',
    params: params
  })
}
