import request from '@/utils/request'

// 查询商品评论管理列表
export function listComments(query) {
  return request({
    url: '/system/comments/list',
    method: 'get',
    params: query
  })
}

// 查询商品评论管理详细
export function getComments(commentId) {
  return request({
    url: '/system/comments/' + commentId,
    method: 'get'
  })
}

// 新增商品评论管理
export function addComments(data) {
  return request({
    url: '/system/comments',
    method: 'post',
    data: data
  })
}

// 修改商品评论管理
export function updateComments(data) {
  return request({
    url: '/system/comments',
    method: 'put',
    data: data
  })
}

// 删除商品评论管理
export function delComments(commentId) {
  return request({
    url: '/system/comments/' + commentId,
    method: 'delete'
  })
}
