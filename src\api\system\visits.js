import request from '@/utils/request'

// 查询邀请码访问记录列表
export function listVisits(query) {
  return request({
    url: '/system/visits/list',
    method: 'get',
    params: query
  })
}

// 查询邀请码访问记录详细
export function getVisits(id) {
  return request({
    url: '/system/visits/' + id,
    method: 'get'
  })
}

// 新增邀请码访问记录
export function addVisits(data) {
  return request({
    url: '/system/visits',
    method: 'post',
    data: data
  })
}

// 修改邀请码访问记录
export function updateVisits(data) {
  return request({
    url: '/system/visits',
    method: 'put',
    data: data
  })
}

// 删除邀请码访问记录
export function delVisits(id) {
  return request({
    url: '/system/visits/' + id,
    method: 'delete'
  })
}
