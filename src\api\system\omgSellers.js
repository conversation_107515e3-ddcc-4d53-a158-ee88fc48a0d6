import request from '@/utils/request'

// 查询omg_卖家店铺列表
export function listOmgSellers(query) {
  return request({
    url: '/system/omgSellers/list',
    method: 'get',
    params: query
  })
}

// 查询omg_卖家店铺详细
export function getOmgSellers(sellerId) {
  return request({
    url: '/system/omgSellers/' + sellerId,
    method: 'get'
  })
}

// 新增omg_卖家店铺
export function addOmgSellers(data) {
  return request({
    url: '/system/omgSellers',
    method: 'post',
    data: data
  })
}

// 修改omg_卖家店铺
export function updateOmgSellers(data) {
  return request({
    url: '/system/omgSellers',
    method: 'put',
    data: data
  })
}

// 删除omg_卖家店铺
export function delOmgSellers(sellerId) {
  return request({
    url: '/system/omgSellers/' + sellerId,
    method: 'delete'
  })
}
