<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="商品名称" prop="name">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入商品名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="所需积分" prop="points">
                    <el-input
                        v-model="queryParams.points"
                        placeholder="请输入所需积分"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="库存" prop="stock">
                    <el-input
                        v-model="queryParams.stock"
                        placeholder="请输入库存"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="是否激活" prop="isActive">
                    <el-select v-model="queryParams.isActive" placeholder="请选择状态" clearable style="width: 140px">
                      <el-option label="已激活" :value="1" />
                      <el-option label="未激活" :value="0" />
                    </el-select>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:items:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:items:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:items:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:items:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="itemsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="ID" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="商品名称" align="center" prop="name" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="所需积分" align="center" prop="points" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="商品类型" align="center" prop="type" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="商品图片" align="center" prop="image" width="100" v-if="columns[4].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.image" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="库存" align="center" prop="stock" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="商品描述" align="center" prop="description" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="是否激活" align="center" prop="isActive" v-if="columns[7].visible">
                  <template #default="scope">
                    <div class="status-tag">
                      <span class="status-dot" :class="getActiveClass(scope.row.isActive)"></span>
                      <span>{{ getActiveText(scope.row.isActive) }}</span>
                    </div>
                  </template>
                </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:items:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:items:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改积分兑换商品对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="itemsRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="商品名称" prop="name">
                          <el-input v-model="form.name" placeholder="请输入商品名称" />
                        </el-form-item>
                        <el-form-item label="所需积分" prop="points">
                          <el-input v-model="form.points" placeholder="请输入所需积分" />
                        </el-form-item>
                        <el-form-item label="商品图片" prop="image">
                          <image-upload v-model="form.image"/>
                        </el-form-item>
                        <el-form-item label="库存" prop="stock">
                          <el-input v-model="form.stock" placeholder="请输入库存" />
                        </el-form-item>
                        <el-form-item label="商品描述" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="是否激活" prop="isActive">
                          <el-switch
                            v-model="form.isActive"
                            :active-value="1"
                            :inactive-value="0"
                            active-text="已激活"
                            inactive-text="未激活"
                          />
                        </el-form-item>
                        <el-form-item label="商品类型" prop="type">
                          <el-select v-model="form.type" placeholder="请选择商品类型">
                            <el-option label="coupon" value="coupon" />
                            <el-option label="product" value="product" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="创建时间" prop="createdAt">
                          <el-date-picker clearable
                                          v-model="form.createdAt"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择创建时间">
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item label="更新时间" prop="updatedAt">
                          <el-date-picker clearable
                                          v-model="form.updatedAt"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择更新时间">
                          </el-date-picker>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Items">
  import { listItems, getItems, delItems, addItems, updateItems } from "@/api/system/items";

  const { proxy } = getCurrentInstance();

  const itemsList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    name: null,
                    points: null,
                    type: null,
                    image: null,
                    stock: null,
                    description: null,
                    isActive: null,
    },
    rules: {
                    name: [
                { required: true, message: "商品名称不能为空", trigger: "blur" }
              ],
                    points: [
                { required: true, message: "所需积分不能为空", trigger: "blur" }
              ],
                    type: [
                { required: true, message: "商品类型不能为空", trigger: "change" }
              ],
                    stock: [
                { required: true, message: "库存不能为空", trigger: "blur" }
              ],
                    isActive: [
                { required: true, message: "是否激活不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: 'ID', visible: true },
                { key: 1, label: '商品名称', visible: true },
                { key: 2, label: '所需积分', visible: true },
                { key: 3, label: '商品类型', visible: true },
                { key: 4, label: '商品图片', visible: true },
                { key: 5, label: '库存', visible: true },
                { key: 6, label: '商品描述', visible: true },
                { key: 7, label: '是否激活', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询积分兑换商品列表 */
  function getList() {
    loading.value = true;
    listItems(queryParams.value).then(response => {
            itemsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    name: null,
                    points: null,
                    type: null,
                    image: null,
                    stock: null,
                    description: null,
                    isActive: null,
                    createdAt: null,
                    updatedAt: null
    };
    proxy.resetForm("itemsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加积分兑换商品";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getItems(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改积分兑换商品";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["itemsRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateItems(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addItems(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除积分兑换商品编号为"' + _ids + '"的数据项？').then(function() {
      return delItems(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/items/export', {
      ...queryParams.value
    }, `items_${new Date().getTime()}.xlsx`)
  }

  /** 获取激活状态对应的类名 */
  function getActiveClass(isActive) {
    return isActive === 1 || isActive === '1' || isActive === true || isActive === 'true' ? 'status-success' : 'status-danger';
  }

  /** 获取激活状态对应的文字 */
  function getActiveText(isActive) {
    return isActive === 1 || isActive === '1' || isActive === true || isActive === 'true' ? '已激活' : '未激活';
  }

  getList();
</script>

<style scoped>
.status-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-success {
  background-color: #67C23A;
}

.status-danger {
  background-color: #F56C6C;
}

.status-warning {
  background-color: #E6A23C;
}

.status-info {
  background-color: #909399;
}

.status-default {
  background-color: #DCDFE6;
}
</style>
