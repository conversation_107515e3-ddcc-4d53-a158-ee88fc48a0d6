import request from '@/utils/request'

// 查询帖子管理列表
export function listPosts(query) {
  return request({
    url: '/system/posts/detail/list',
    method: 'get',
    params: query
  })
}

// 查询帖子管理详细
export function getPosts(id) {
  return request({
    url: '/system/posts/' + id,
    method: 'get'
  })
}

// 查询帖子详情信息（包含标签、图片、评论）
export function getPostDetail(id) {
  return request({
    url: '/system/posts/detail/' + id,
    method: 'get'
  })
}

// 新增帖子管理
export function addPosts(data) {
  return request({
    url: '/system/posts',
    method: 'post',
    data: data
  })
}

// 修改帖子管理
export function updatePosts(data) {
  return request({
    url: '/system/posts',
    method: 'put',
    data: data
  })
}

// 更新帖子详情信息（包含标签、图片、评论）
export function updatePostDetail(data) {
  return request({
    url: '/system/posts/detail',
    method: 'put',
    data: data
  })
}

// 删除帖子管理
export function delPosts(id) {
  return request({
    url: '/system/posts/' + id,
    method: 'delete'
  })
}
