import request from '@/utils/request'

// 查询OMG平台链接列表
export function listOmgPlatformLinks(query) {
  return request({
    url: '/system/OmgPlatformLinks/list',
    method: 'get',
    params: query
  })
}

// 查询OMG平台链接详细
export function getOmgPlatformLinks(id) {
  return request({
    url: '/system/OmgPlatformLinks/' + id,
    method: 'get'
  })
}

// 新增OMG平台链接
export function addOmgPlatformLinks(data) {
  return request({
    url: '/system/OmgPlatformLinks',
    method: 'post',
    data: data
  })
}

// 修改OMG平台链接
export function updateOmgPlatformLinks(data) {
  return request({
    url: '/system/OmgPlatformLinks',
    method: 'put',
    data: data
  })
}

// 删除OMG平台链接
export function delOmgPlatformLinks(id) {
  return request({
    url: '/system/OmgPlatformLinks/' + id,
    method: 'delete'
  })
}
