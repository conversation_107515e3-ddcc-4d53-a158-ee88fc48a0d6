import request from '@/utils/request'

// 查询商品轮播图列表
export function listBanners(query) {
  return request({
    url: '/system/banners/list',
    method: 'get',
    params: query
  })
}

// 查询商品轮播图详细
export function getBanners(id) {
  return request({
    url: '/system/banners/' + id,
    method: 'get'
  })
}

// 新增商品轮播图
export function addBanners(data) {
  return request({
    url: '/system/banners',
    method: 'post',
    data: data
  })
}

// 修改商品轮播图
export function updateBanners(data) {
  return request({
    url: '/system/banners',
    method: 'put',
    data: data
  })
}

// 删除商品轮播图
export function delBanners(id) {
  return request({
    url: '/system/banners/' + id,
    method: 'delete'
  })
}
