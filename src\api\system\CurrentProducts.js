import request from '@/utils/request'

// 查询最新商品列表
export function listCurrentProducts(query) {
  return request({
    url: '/system/CurrentProducts/list',
    method: 'get',
    params: query
  })
}

// 查询最新商品详细
export function getCurrentProducts(id) {
  return request({
    url: '/system/CurrentProducts/' + id,
    method: 'get'
  })
}

// 新增最新商品
export function addCurrentProducts(data) {
  return request({
    url: '/system/CurrentProducts',
    method: 'post',
    data: data
  })
}

// 修改最新商品
export function updateCurrentProducts(data) {
  return request({
    url: '/system/CurrentProducts',
    method: 'put',
    data: data
  })
}

// 删除最新商品
export function delCurrentProducts(id) {
  return request({
    url: '/system/CurrentProducts/' + id,
    method: 'delete'
  })
}
