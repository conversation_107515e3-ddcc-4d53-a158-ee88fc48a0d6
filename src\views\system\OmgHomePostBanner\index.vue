<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="帖子id" prop="postId">
                    <el-input
                        v-model="queryParams.postId"
                        placeholder="请输入帖子id"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="顺序" prop="sortOrder">
                    <el-input
                        v-model="queryParams.sortOrder"
                        placeholder="请输入顺序"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-date-picker clearable
                                    v-model="queryParams.createdAt"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择创建时间">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgHomePostBanner:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgHomePostBanner:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgHomePostBanner:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgHomePostBanner:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgHomePostBannerList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="id" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="帖子id" align="center" prop="postId" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="顺序" align="center" prop="sortOrder" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="创建时间" align="center" prop="createdAt" width="180" v-if="columns[3].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgHomePostBanner:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgHomePostBanner:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg首页帖子轮播图对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgHomePostBannerRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="帖子id" prop="postId">
                          <el-input v-model="form.postId" placeholder="请输入帖子id" />
                        </el-form-item>
                        <el-form-item label="顺序" prop="sortOrder">
                          <el-input v-model="form.sortOrder" placeholder="请输入顺序" />
                        </el-form-item>
                        <el-form-item label="创建时间" prop="createdAt">
                          <el-date-picker clearable
                                          v-model="form.createdAt"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择创建时间">
                          </el-date-picker>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgHomePostBanner">
  import { listOmgHomePostBanner, getOmgHomePostBanner, delOmgHomePostBanner, addOmgHomePostBanner, updateOmgHomePostBanner } from "@/api/system/OmgHomePostBanner";

  const { proxy } = getCurrentInstance();

  const OmgHomePostBannerList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    postId: null,
                    sortOrder: null,
                    createdAt: null
    },
    rules: {
    },
    //表格展示列
    columns: [
              { key: 0, label: 'id', visible: true },
                { key: 1, label: '帖子id', visible: true },
                { key: 2, label: '顺序', visible: true },
                { key: 3, label: '创建时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg首页帖子轮播图列表 */
  function getList() {
    loading.value = true;
    listOmgHomePostBanner(queryParams.value).then(response => {
            OmgHomePostBannerList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    postId: null,
                    sortOrder: null,
                    createdAt: null
    };
    proxy.resetForm("OmgHomePostBannerRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加omg首页帖子轮播图";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getOmgHomePostBanner(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改omg首页帖子轮播图";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgHomePostBannerRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateOmgHomePostBanner(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgHomePostBanner(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除omg首页帖子轮播图编号为"' + _ids + '"的数据项？').then(function() {
      return delOmgHomePostBanner(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgHomePostBanner/export', {
      ...queryParams.value
    }, `OmgHomePostBanner_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
