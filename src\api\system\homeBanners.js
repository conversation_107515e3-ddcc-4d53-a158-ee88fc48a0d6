import request from '@/utils/request'

// 查询omg首页轮播图列表
export function listHomeBanners(query) {
  return request({
    url: '/system/homeBanners/list',
    method: 'get',
    params: query
  })
}

// 查询omg首页轮播图详细
export function getHomeBanners(id) {
  return request({
    url: '/system/homeBanners/' + id,
    method: 'get'
  })
}

// 新增omg首页轮播图
export function addHomeBanners(data) {
  return request({
    url: '/system/homeBanners',
    method: 'post',
    data: data
  })
}

// 修改omg首页轮播图
export function updateHomeBanners(data) {
  return request({
    url: '/system/homeBanners',
    method: 'put',
    data: data
  })
}

// 删除omg首页轮播图
export function delHomeBanners(id) {
  return request({
    url: '/system/homeBanners/' + id,
    method: 'delete'
  })
}
