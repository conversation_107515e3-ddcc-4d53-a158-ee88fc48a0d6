<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="80px">
                  <el-form-item label="商品名称" prop="name">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入商品名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <!-- <el-form-item label="商品标识符" prop="slug">
                    <el-input
                        v-model="queryParams.slug"
                        placeholder="请输入商品标识符"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item> -->
                  <el-form-item label="商品价格" prop="price">
                    <el-input
                        v-model="queryParams.price"
                        placeholder="请输入商品价格"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="卖家名称" prop="sellerName">
                    <el-input
                        v-model="queryParams.sellerName"
                        placeholder="请输入卖家名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="平台名称" prop="platformName">
                    <el-select v-model="queryParams.platformName" placeholder="请选择平台" clearable style="width: 240px;">
                      <el-option label="微店" value="weidian" />
                      <el-option label="淘宝" value="taobao" />
                      <el-option label="1688" value="1688" />
                    </el-select>
                  </el-form-item>
                  <el-form-item label="商品分类" prop="categoryId">
                      <el-select v-model="queryParams.categoryId" placeholder="请选择商品分类" clearable style="width: 240px;">
                          <el-option
                              v-for="category in categoryOptions"
                              :key="category.categoryId"
                              :label="category.name"
                              :value="category.categoryId"
                          />
                      </el-select>
                  </el-form-item>
                  <el-form-item label="创建时间" prop="createdAt">
                    <el-date-picker clearable
                                    v-model="queryParams.createdAt"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择创建时间">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:products:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:products:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:products:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:products:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="info"
            plain
            icon="Upload"
            @click="handleImport"
            v-hasPermi="['system:products:import']" >批量导入</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="info"
            plain
            icon="Download"
            @click="importTemplate"
            v-hasPermi="['system:products:import']" >下载模板</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Star"
            @click="handleDailyProduct"
            v-hasPermi="['system:products:edit']"
        >每日一品</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="HotWater"
            @click="handleHotProducts"
            v-hasPermi="['system:products:edit']"
        >最热商品</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="StarFilled"
            @click="handleNewProducts"
            v-hasPermi="['system:products:edit']"
        >最新商品</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>
    <el-table v-loading="loading" :data="productsList" @selection-change="handleSelectionChange">
                  <el-table-column type="selection" width="30" align="center" />
                  <el-table-column label="序号" align="center" prop="productId" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品名称" align="center" prop="name" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品标识符" align="center" prop="slug" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品描述" align="center" prop="description" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品价格" align="center" prop="price" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品原价" align="center" prop="originalPrice" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品主图链接" align="center" prop="mainImage" width="100" v-if="columns[6].visible">
              <template #default="scope">
                <image-preview :src="scope.row.mainImage" :width="50" :height="50"/>
              </template>
            </el-table-column>
            <el-table-column label="商品编号" align="center" prop="sku" v-if="columns[7].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="卖家名称" align="center" prop="sellerName" v-if="columns[8].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="平台名称" align="center" prop="platformName" v-if="columns[9].visible" :show-overflow-tooltip="true">
              <template #default="scope">
                <span>{{ getPlatformText(scope.row.platformName) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="点赞数" align="center" prop="likes" v-if="columns[10].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="浏览量" align="center" prop="views" v-if="columns[11].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="收藏量" align="center" prop="collect" v-if="columns[15].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品评分" align="center" prop="rating" v-if="columns[12].visible" :show-overflow-tooltip="true"/>
            <el-table-column label="商品状态" align="center" prop="status" v-if="columns[13].visible">
        <template #default="scope">
          <div class="status-tag">
            <span class="status-dot" :class="getStatusClass(scope.row.status)"></span>
            <span>{{ getStatusText(scope.row.status) }}</span>
          </div>
        </template>
      </el-table-column>
            <el-table-column label="创建时间" align="center" prop="createdAt" width="180" v-if="columns[14].visible" :show-overflow-tooltip="true">
              <template #default="scope">
                <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="180">
            <template #default="scope">
              <div class="operation-buttons">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:products:edit']">修改</el-button>
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:products:remove']">删除</el-button>
                <el-button link type="success" v-if="scope.row.status === 'inactive'" @click="handleActive(scope.row)">上架</el-button>
                <el-button link type="warning" v-else-if="scope.row.status === 'active'" @click="handleInactive(scope.row)">下架</el-button>
              </div>
            </template>
          </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改商品管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="productsRef" :model="form" :rules="rules" label-width="100px">
                        <el-form-item label="商品名称" prop="name">
                          <el-input v-model="form.name" placeholder="请输入商品名称" />
                        </el-form-item>
                        <el-form-item label="商品标识符" prop="slug">
                          <el-input v-model="form.slug" placeholder="请输入商品标识符" />
                        </el-form-item>
                        <el-form-item label="商品描述" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="商品价格" prop="price">
                          <el-input v-model="form.price" placeholder="请输入商品价格" />
                        </el-form-item>
                        <el-form-item label="商品原价" prop="originalPrice">
                          <el-input v-model="form.originalPrice" placeholder="请输入商品原价" />
                        </el-form-item>
                        <el-form-item label="商品分类" prop="categoryId">
                            <el-select v-model="form.categoryId" clearable placeholder="请选择商品分类">
                                <el-option
                                    v-for="category in categoryOptions"
                                    :key="category.categoryId"
                                    :label="category.name"
                                    :value="category.categoryId"
                                />
                            </el-select>
                        </el-form-item>
                        <el-form-item label="商品主图链接" prop="mainImage">
                          <image-upload v-model="form.mainImage" :limit="1" />
                        </el-form-item> 
                        <el-form-item label="商品QC图" prop="qcImages">
                          <image-upload v-model="form.qcImages" :limit="10" />
                        </el-form-item>
                        <el-form-item label="商品编号" prop="sku">
                          <el-input v-model="form.sku" placeholder="请输入商品编号" />
                        </el-form-item>
                        <el-form-item label="商品库存" prop="stock">
                          <el-input v-model="form.stock" placeholder="请输入商品库存" />
                        </el-form-item>
                        <el-form-item label="卖家名称" prop="sellerName">
                          <el-input v-model="form.sellerName" placeholder="请输入卖家名称" />
                        </el-form-item>
                        <el-form-item label="平台名称" prop="platformName">
                          <el-select v-model="form.platformName" placeholder="请选择平台">
                            <el-option label="微店" value="weidian" />
                            <el-option label="淘宝" value="taobao" />
                            <el-option label="1688" value="1688" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="点赞数" prop="likes">
                          <el-input v-model="form.likes" placeholder="请输入点赞数" />
                        </el-form-item>
                        <el-form-item label="浏览量" prop="views">
                          <el-input v-model="form.views" placeholder="请输入浏览量" />
                        </el-form-item>
                        <el-form-item label="收藏量" prop="collect">
                          <el-input v-model="form.collect" placeholder="请输入收藏量" />
                        </el-form-item>
                        <el-form-item label="商品评分" prop="rating">
                          <el-input v-model="form.rating" placeholder="请输入商品评分" />
                        </el-form-item>
                        <!-- <el-form-item label="评分总数" prop="totalRatings">
                          <el-input v-model="form.totalRatings" placeholder="请输入评分总数" />
                        </el-form-item>
                        <el-form-item label="创建时间" prop="createdAt">
                          <el-date-picker clearable
                                          v-model="form.createdAt"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择创建时间">
                          </el-date-picker> -->
                        <!-- </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 导入对话框 -->
    <el-dialog v-model="importOpen" title="批量导入商品" width="500px" append-to-body>
      <div class="import-tips">
        <p class="import-warning">导入注意事项：</p>
        <ul>
          <li>请不要在Excel中填写product_id列，系统会自动生成ID</li>
          <li>请确保slug(商品标识符)在系统中唯一</li>
          <li>价格、原价等数值字段请使用有效数字</li>
          <li>商品状态请填写"active"或"inactive"</li>
        </ul>
        <el-button type="primary" link @click="importTemplate">
          <el-icon><Download /></el-icon>下载导入模板
        </el-button>
      </div>
      <el-upload
        class="upload-demo"
        drag
        action="#"
        :auto-upload="false"
        :on-change="handleFileChange"
        :limit="1"
        accept=".xlsx, .xls"
      >
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">
          拖拽文件到此处，或 <em>点击上传</em>
        </div>
        <template #tip>
          <div class="el-upload__tip">
            只能上传 Excel 文件 (xlsx, xls)
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitImport" :disabled="!uploadFile">确 定</el-button>
          <el-button @click="cancelImport">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 每日一品对话框 -->
    <el-dialog title="设置每日一品" v-model="dailyProductOpen" width="800px" append-to-body>
      <el-form ref="dailyProductRef" :model="dailyProductForm" label-width="100px">
        <el-form-item label="选择商品" prop="productId">
          <el-select
            v-model="dailyProductForm.productId"
            filterable
            remote
            :remote-method="handleSearch"
            placeholder="请输入商品名称搜索"
            :loading="searchLoading"
            style="width: 100%"
            @change="handleProductChange"
            :default-first-option="true"
            clearable
          >
            <el-option
              
              v-for="item in productOptions"
              :key="item.productId"
              :label="item.name"
              :value="item.productId"
            >
              <div style="display: flex; align-items: center;">
                <el-image 
                  :src="item.mainImage" 
                  style="width: 40px; height: 40px; margin-right: 10px; object-fit: cover;"
                  :preview-src-list="[item.mainImage]"
                />
                <div>
                  <div style="font-weight: bold;">{{ item.name }}</div>
                  <div style="font-size: 12px; color: #666;">
                    <span>价格: ¥{{ item.price }}</span>
                    <span style="margin-left: 10px;">库存: {{ item.stock }}</span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="商品信息" v-if="selectedProduct">
          <div class="selected-product-info">
            <el-image 
              :src="selectedProduct.mainImage" 
              style="width: 100px; height: 100px; object-fit: cover;"
              :preview-src-list="[selectedProduct.mainImage]"
            />
            <div class="product-details">
              <p><strong>商品名称：</strong>{{ selectedProduct.name }}</p>
              <p><strong>商品价格：</strong>¥{{ selectedProduct.price }}</p>
              <p><strong>商品库存：</strong>{{ selectedProduct.stock }}</p>
              <p><strong>商品状态：</strong>{{ getStatusText(selectedProduct.status) }}</p>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitDailyProduct">{{ isUpdateMode ? '修 改' : '确 定' }}</el-button>
          <el-button @click="cancelDailyProduct">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 最热商品对话框 -->
    <el-dialog title="设置最热商品" v-model="hotProductsOpen" width="800px" append-to-body>
      <el-form ref="hotProductsRef" :model="hotProductsForm" label-width="100px">
        <el-form-item label="选择商品" prop="productIds">
          <el-select
            v-model="hotProductsForm.productIds"
            multiple
            filterable
            remote
            :remote-method="handleHotProductSearch"
            placeholder="请输入商品名称搜索(最多选择4个)"
            :loading="hotProductSearchLoading"
            style="width: 100%"
            :default-first-option="true"
            clearable
            :multiple-limit="4"
          >
            <el-option
              
              v-for="item in hotProductOptions"
              :key="item.productId"
              :label="item.name"
              :value="item.productId"
            >
              <div style="display: flex; align-items: center;">
                <el-image 
                  :src="item.mainImage" 
                  style="width: 40px; height: 40px; margin-right: 10px; object-fit: cover;"
                  :preview-src-list="[item.mainImage]"
                />
                <div>
                  <div style="font-weight: bold;">{{ item.name }}</div>
                  <div style="font-size: 12px; color: #666;">
                    <span>价格: ¥{{ item.price }}</span>
                    <span style="margin-left: 10px;">库存: {{ item.stock }}</span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="已选商品" v-if="selectedHotProducts.length > 0">
          <div class="selected-products-list">
            <div v-for="product in selectedHotProducts" :key="product.productId" class="selected-product-item">
              <el-image 
                :src="product.mainImage" 
                style="width: 60px; height: 60px; object-fit: cover;"
                :preview-src-list="[product.mainImage]"
              />
              <div class="product-details">
                <p class="product-name">{{ product.name }}</p>
                <p class="product-price">¥{{ product.price }}</p>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitHotProducts">{{ isHotProductsUpdateMode ? '修 改' : '确 定' }}</el-button>
          <el-button @click="cancelHotProducts">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 最新商品对话框 -->
    <el-dialog title="设置最新商品（请选择7个商品）" v-model="newProductsOpen" width="800px" append-to-body>
      <el-form ref="newProductsRef" :model="newProductsForm" label-width="100px">
        <el-form-item label="选择商品" prop="productIds">
          <el-select
            v-model="newProductsForm.productIds"
            multiple
            filterable
            remote
            :remote-method="handleNewProductSearch"
            placeholder="请选择7个商品"
            :loading="newProductSearchLoading"
            style="width: 100%"
            :default-first-option="true"
            clearable
            :multiple-limit="7"
          >
            <el-option
              
              v-for="item in newProductOptions"
              :key="item.productId"
              :label="item.name"
              :value="item.productId"
            >
              <div style="display: flex; align-items: center;">
                <el-image 
                  :src="item.mainImage" 
                  style="width: 40px; height: 40px; margin-right: 10px; object-fit: cover;"
                  :preview-src-list="[item.mainImage]"
                />
                <div>
                  <div style="font-weight: bold;">{{ item.name }}</div>
                  <div style="font-size: 12px; color: #666;">
                    <span>价格: ¥{{ item.price }}</span>
                    <span style="margin-left: 10px;">库存: {{ item.stock }}</span>
                  </div>
                </div>
              </div>
            </el-option>
          </el-select>
          <div class="selection-tip" style="margin-top: 5px; color: #f56c6c;">
            注意：必须选择7个商品，不多不少
          </div>
        </el-form-item>
        <el-form-item label="已选商品" v-if="selectedNewProducts.length > 0">
          <div class="selected-products-list">
            <div v-for="product in selectedNewProducts" :key="product.productId" class="selected-product-item">
              <el-image 
                :src="product.mainImage" 
                style="width: 60px; height: 60px; object-fit: cover;"
                :preview-src-list="[product.mainImage]"
              />
              <div class="product-details">
                <p class="product-name">{{ product.name }}</p>
                <p class="product-price">¥{{ product.price }}</p>
              </div>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitNewProducts">{{ isNewProductsUpdateMode ? '修 改' : '确 定' }}</el-button>
          <el-button @click="cancelNewProducts">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Products">
  import { listProducts, getProducts, delProducts, addProducts, updateProducts, updateProductStatus, importProducts, addDailyProduct, checkExistTodayDailyProduct, updateDailyProduct, addHotProducts, checkExistHotProducts, updateHotProducts, getCurrentHotProducts, checkExistCurrentProducts, getCurrentNewProducts, addCurrentProducts, updateCurrentProducts } from "@/api/system/products";
  import { UploadFilled, Download, HotWater, StarFilled } from '@element-plus/icons-vue';
  import { getAllCategories } from '@/api/system/categories'; // 引入获取所有分类的API，更正路径

  const { proxy } = getCurrentInstance();

  const productsList = ref([]);
  const open = ref(false);
  const importOpen = ref(false); // 导入对话框显示状态
  const uploadFile = ref(null); // 上传的文件
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  const dailyProductOpen = ref(false);
  const searchLoading = ref(false);
  const productOptions = ref([]);
  const dailyProductForm = ref({
    productId: null
  });
  const selectedProduct = ref(null);
  const isUpdateMode = ref(false); // 新增标记，用于控制按钮文字
  const hotProductsOpen = ref(false);
  const hotProductSearchLoading = ref(false);
  const hotProductOptions = ref([]);
  const hotProductsForm = ref({
    productIds: []
  });
  const selectedHotProducts = ref([]);
  const isHotProductsUpdateMode = ref(false);
  const existingHotProducts = ref([]);
  const newProductsOpen = ref(false);
  const newProductSearchLoading = ref(false);
  const newProductOptions = ref([]);
  const newProductsForm = ref({
    productIds: []
  });
  const selectedNewProducts = ref([]);
  const isNewProductsUpdateMode = ref(false);
  const existingNewProducts = ref([]);
  const categoryOptions = ref([]); // 新增：分类选项列表

  const data = reactive({
    form: {
      productId: null,
      sellerId: null,
      name: null,
      slug: null,
      description: null,
      price: null,
      originalPrice: null,
      mainImage: null,
      qcImages: [],
      sku: null,
      stock: null,
      likes: null,
      views: null,
      rating: null,
      totalRatings: null,
      status: null,
      createdAt: null,
      updatedAt: null,
      categoryId: null, // 新增：商品分类ID
      sellerName: null, // 新增：卖家名称
      platformName: null, // 新增：平台名称
    },
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      name: null,
      slug: null,
      description: null,
      price: null,
      status: null,
      createdAt: null,
      categoryId: null, // 新增：查询参数中的商品分类ID
      sellerName: null, // 新增：查询参数中的卖家名称
      platformName: null, // 新增：查询参数中的平台名称
    },
    rules: {
      name: [
        { required: true, message: "商品名称不能为空", trigger: "blur" }
      ],
      slug: [
        { required: true, message: "商品标识符不能为空", trigger: "blur" }
      ],
      price: [
        { required: true, message: "商品价格不能为空", trigger: "blur" }
      ],
      categoryId: [
        { required: true, message: "商品分类不能为空", trigger: "change" }
      ], // 新增：商品分类ID的校验规则
    },
    //表格展示列
    columns: [
      { key: 0, label: '商品唯一标识', visible: true },
      { key: 1, label: '商品名称', visible: true },
      { key: 2, label: '商品标识符', visible: true },
      { key: 3, label: '商品描述', visible: true },
      { key: 4, label: '商品价格', visible: true },
      { key: 5, label: '商品原价', visible: true },
      { key: 6, label: '商品主图链接', visible: true },
      { key: 7, label: '商品编号', visible: true },
      { key: 8, label: '卖家名称', visible: true },
      { key: 9, label: '平台名称', visible: true },
      { key: 10, label: '点赞数', visible: true },
      { key: 11, label: '浏览量', visible: true },
      { key: 12, label: '商品评分', visible: true },
      { key: 13, label: '商品状态', visible: true },
      { key: 14, label: '创建时间', visible: true },
      { key: 15, label: '收藏量', visible: true },
    ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询商品管理列表 */
  function getList() {
    loading.value = true;
    listProducts(queryParams.value).then(response => {
      productsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  /** 上架按钮操作 */
  function handleActive(row) {
    proxy.$modal.confirm('是否确认上架商品"' + row.name + '"？').then(() => {
      return updateProductStatus(row.productId, 'active');
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("上架成功");
    }).catch(() => {});
  }

  /** 下架按钮操作 */
  function handleInactive(row) {
    proxy.$modal.confirm('是否确认下架商品"' + row.name + '"？').then(() => {
      return updateProductStatus(row.productId, 'inactive');
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("下架成功");
    }).catch(() => {});
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
      productId: null,
      sellerId: null,
      name: null,
      slug: null,
      description: null,
      price: null,
      originalPrice: null,
      mainImage: null,
      qcImages: [],
      sku: null,
      stock: null,
      likes: null,
      views: null,
      rating: null,
      totalRatings: null,
      status: null,
      createdAt: null,
      updatedAt: null,
      categoryId: null, // 新增：重置时清空分类ID
      sellerName: null, // 新增：重置时清空卖家名称
      platformName: null, // 新增：重置时清空平台名称
    };
    proxy.resetForm("productsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.productId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加商品管理";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _productId = row.productId || ids.value
    getProducts(_productId).then(response => {
      form.value = response.data;
      
      console.log('原始QC图片数据:', JSON.stringify(form.value.qcImages));
      
      // 如果qcImages是对象数组，提取URL字符串（因为组件期望是URL字符串或URL数组）
      if (form.value.qcImages && Array.isArray(form.value.qcImages)) {
        const urls = form.value.qcImages.map(item => item.imageUrl).filter(Boolean);
        form.value.qcImages = urls.join(','); // 转为逗号分隔的字符串，组件内部会处理
        console.log('转换后的QC图片字符串:', form.value.qcImages);
      } else if (!form.value.qcImages) {
        form.value.qcImages = '';
      }
      
      open.value = true;
      title.value = "修改商品管理";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["productsRef"].validate(valid => {
      if (valid) {
        // 创建表单数据的副本，避免直接修改表单绑定值
        const formData = { ...form.value };
        
        console.log('提交前的QC图片数据:', formData.qcImages);
        
        // 处理QC图片数据
        // ImageUpload组件返回的是逗号分隔的URL字符串
        if (typeof formData.qcImages === 'string') {
          const urls = formData.qcImages.split(',').filter(url => url.trim() !== '');
          formData.qcImages = urls.map(url => ({
            imageUrl: url,
            displayOrder: 0
          }));
          console.log('转换后准备提交的QC图片数据:', JSON.stringify(formData.qcImages));
        } else if (!formData.qcImages) {
          formData.qcImages = [];
        }
        
        if (formData.productId != null) {
          updateProducts(formData).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addProducts(formData).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _productIds = row.productId || ids.value;
    proxy.$modal.confirm('是否确认删除商品管理编号为"' + _productIds + '"的数据项？').then(function() {
      return delProducts(_productIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 获取状态对应的类名 */
  function getStatusClass(status) {
    const statusMap = {
      active: 'status-success',
      inactive: 'status-warning',
      out_of_stock: 'status-danger',
      draft: 'status-info'
    };
    return statusMap[status] || 'status-default';
  }

  /** 获取状态对应的文字 */
  function getStatusText(status) {
    const statusTextMap = {
      active: '正常',
      inactive: '下架',
      out_of_stock: '缺货',
      draft: '草稿'
    };
    return statusTextMap[status] || status;
  }

  /** 获取平台对应的文字 */
  function getPlatformText(platform) {
    const platformTextMap = {
      weidian: '微店',
      taobao: '淘宝',
      1688: '1688'
    };
    return platformTextMap[platform] || platform;
  }

  /** 导入按钮操作 */
  function handleImport() {
    importOpen.value = true;
    uploadFile.value = null;
  }

  /** 下载模板操作 */
  function importTemplate() {
    proxy.download("system/products/importTemplate", {}, 
      `products_template_${new Date().getTime()}.xlsx`);
  }

  /** 取消导入 */
  function cancelImport() {
    importOpen.value = false;
    uploadFile.value = null;
  }

  /** 文件状态改变 */
  function handleFileChange(file) {
    uploadFile.value = file.raw;
  }

  /** 提交导入 */
  function submitImport() {
    if (!uploadFile.value) {
      proxy.$modal.msgError("请选择要上传的文件");
      return;
    }
    
    proxy.$modal.loading("正在导入数据，请稍候...");
    
    importProducts(uploadFile.value).then(response => {
      proxy.$modal.msgSuccess(response.msg);
      importOpen.value = false;
      getList();
    }).catch(error => {
      console.error("导入失败:", error);
    }).finally(() => {
      proxy.$modal.closeLoading();
    });
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/products/export', {
      ...queryParams.value
    }, `products_${new Date().getTime()}.xlsx`)
  }

  /** 每日一品按钮操作 */
  async function handleDailyProduct() {
    dailyProductOpen.value = true;
    // 初始加载所有商品
    handleSearch('');
    
    // 检查今日是否已存在记录
    try {
      const response = await checkExistTodayDailyProduct();
      if (response.msg === "今天已存在记录") {
        isUpdateMode.value = true;
      } else {
        isUpdateMode.value = false;
      }
    } catch (error) {
      console.error("检查每日一品状态失败:", error);
    }
  }

  /** 搜索商品 */
  function handleSearch(query) {
    searchLoading.value = true;
    listProducts({
      pageNum: 1,
      pageSize: 100, // 增加每页显示数量
      name: query,
      status: 'active' // 只搜索上架的商品
    }).then(response => {
      productOptions.value = response.rows;
      searchLoading.value = false;
    });
  }

  /** 选择商品变化 */
  function handleProductChange(productId) {
    if (productId) {
      selectedProduct.value = productOptions.value.find(item => item.productId === productId);
    } else {
      selectedProduct.value = null;
    }
  }

  /** 提交每日一品 */
  function submitDailyProduct() {
    if (!dailyProductForm.value.productId) {
      proxy.$modal.msgError("请选择商品");
      return;
    }

    if (isUpdateMode.value) {
      // 如果是更新模式，显示确认弹窗并使用update接口
      proxy.$modal.confirm('今日已存在每日一品，是否确认更新？').then(() => {
        updateDailyProduct(dailyProductForm.value.productId).then(response => {
          proxy.$modal.msgSuccess("更新成功");
          dailyProductOpen.value = false;
        });
      }).catch(() => {});
    } else {
      // 如果是新增模式，使用addOrUpdate接口
      addDailyProduct(dailyProductForm.value.productId).then(response => {
        proxy.$modal.msgSuccess("设置每日一品成功");
        dailyProductOpen.value = false;
      });
    }
  }

  /** 取消每日一品 */
  function cancelDailyProduct() {
    dailyProductOpen.value = false;
    dailyProductForm.value = {
      productId: null
    };
    selectedProduct.value = null;
    isUpdateMode.value = false;
  }

  /** 最热商品按钮操作 */
  async function handleHotProducts() {
    hotProductsOpen.value = true;
    // 初始加载所有商品
    handleHotProductSearch('');
    
    // 检查是否已存在最热商品记录
    try {
      const checkResponse = await checkExistHotProducts();
      if (checkResponse.msg === "今天已存在记录" || checkResponse.code === 200) {
        isHotProductsUpdateMode.value = true;
        
        // 获取现有热门商品列表
        const listResponse = await getCurrentHotProducts();
        if (listResponse.rows && listResponse.rows.length > 0) {
          existingHotProducts.value = listResponse.rows;
          
          // 预填充选中的商品ID
          hotProductsForm.value.productIds = listResponse.rows.map(item => item.productId);
          
          // 等待商品数据加载完毕后更新已选商品
          await new Promise(resolve => setTimeout(resolve, 500));
          selectedHotProducts.value = hotProductOptions.value.filter(item => 
            hotProductsForm.value.productIds.includes(item.productId)
          );
        }
      } else {
        isHotProductsUpdateMode.value = false;
        existingHotProducts.value = [];
      }
    } catch (error) {
      console.error("检查最热商品状态失败:", error);
    }
  }

  /** 搜索最热商品 */
  function handleHotProductSearch(query) {
    hotProductSearchLoading.value = true;
    listProducts({
      pageNum: 1,
      pageSize: 100, // 增加每页显示数量
      name: query,
      status: 'active' // 只搜索上架的商品
    }).then(response => {
      hotProductOptions.value = response.rows;
      hotProductSearchLoading.value = false;
    });
  }

  /** 监听最热商品选择变化 */
  watch(() => hotProductsForm.value.productIds, (newVal) => {
    if (newVal && newVal.length > 0) {
      selectedHotProducts.value = hotProductOptions.value.filter(item => newVal.includes(item.productId));
    } else {
      selectedHotProducts.value = [];
    }
  }, { deep: true });

  /** 提交最热商品 */
  function submitHotProducts() {
    if (!hotProductsForm.value.productIds || hotProductsForm.value.productIds.length === 0) {
      proxy.$modal.msgError("请至少选择一个商品");
      return;
    }
    
    if (hotProductsForm.value.productIds.length > 4) {
      proxy.$modal.msgError("最多只能选择4个商品");
      return;
    }
    
    if (isHotProductsUpdateMode.value) {
      // 如果是更新模式，显示确认弹窗
      proxy.$modal.confirm('今日已存在热门商品记录，是否确认更新？').then(() => {
        updateHotProducts(hotProductsForm.value.productIds, existingHotProducts.value).then(response => {
          proxy.$modal.msgSuccess("更新成功");
          hotProductsOpen.value = false;
        });
      }).catch(() => {});
    } else {
      // 如果是新增模式，直接提交
      addHotProducts(hotProductsForm.value.productIds).then(response => {
        proxy.$modal.msgSuccess("设置最热商品成功");
        hotProductsOpen.value = false;
      });
    }
  }

  /** 取消最热商品 */
  function cancelHotProducts() {
    hotProductsOpen.value = false;
    hotProductsForm.value.productIds = [];
    selectedHotProducts.value = [];
    isHotProductsUpdateMode.value = false;
    existingHotProducts.value = [];
  }

  /** 最新商品按钮操作 */
  async function handleNewProducts() {
    newProductsOpen.value = true;
    // 重置表单
    newProductsForm.value.productIds = [];
    selectedNewProducts.value = [];
    
    // 先检查今日是否已存在最新商品记录
    try {
      const checkResponse = await checkExistCurrentProducts();
      console.log("检查最新商品结果:", checkResponse);
      
      // 根据检查结果设置模式
      if (checkResponse.code === 200 && checkResponse.msg === "今天已存在记录") {
        isNewProductsUpdateMode.value = true;
        
        // 获取现有最新商品列表
        const listResponse = await getCurrentNewProducts();
        if (listResponse.rows && listResponse.rows.length > 0) {
          existingNewProducts.value = listResponse.rows;
          
          // 预填充选中的商品ID
          newProductsForm.value.productIds = listResponse.rows.map(item => item.productId);
        }
      } else {
        isNewProductsUpdateMode.value = false;
        existingNewProducts.value = [];
      }
      
      // 加载商品列表（放在最后，确保在设置productIds后加载）
      await handleNewProductSearch('');
      
      // 如果有预选商品ID，等待商品数据加载完毕后更新已选商品
      if (newProductsForm.value.productIds.length > 0) {
        await new Promise(resolve => setTimeout(resolve, 500));
        selectedNewProducts.value = newProductOptions.value.filter(item => 
          newProductsForm.value.productIds.includes(item.productId)
        );
      }
      
    } catch (error) {
      console.error("检查最新商品状态失败:", error);
      isNewProductsUpdateMode.value = false;
      // 失败后也需要加载商品列表
      handleNewProductSearch('');
    }
  }

  /** 搜索最新商品 */
  function handleNewProductSearch(query) {
    newProductSearchLoading.value = true;
    listProducts({
      pageNum: 1,
      pageSize: 100, // 增加每页显示数量
      name: query,
      status: 'active' // 只搜索上架的商品
    }).then(response => {
      newProductOptions.value = response.rows;
      newProductSearchLoading.value = false;
    });
  }

  /** 监听最新商品选择变化 */
  watch(() => newProductsForm.value.productIds, (newVal) => {
    if (newVal && newVal.length > 0) {
      selectedNewProducts.value = newProductOptions.value.filter(item => newVal.includes(item.productId));
    } else {
      selectedNewProducts.value = [];
    }
  }, { deep: true });

  /** 提交最新商品 */
  function submitNewProducts() {
    if (!newProductsForm.value.productIds || newProductsForm.value.productIds.length === 0) {
      proxy.$modal.msgError("请选择7个商品");
      return;
    }
    
    if (newProductsForm.value.productIds.length !== 7) {
      proxy.$modal.msgError("必须选择7个商品，不多不少");
      return;
    }
    
    if (isNewProductsUpdateMode.value) {
      // 如果是更新模式，显示确认弹窗
      proxy.$modal.confirm('今日已存在最新商品记录，是否确认更新？').then(() => {
        // 调用更新接口
        updateCurrentProducts(newProductsForm.value.productIds, existingNewProducts.value).then(response => {
          if (response.code === 200) {
            proxy.$modal.msgSuccess("更新成功");
            newProductsOpen.value = false;
          } else {
            proxy.$modal.msgError(response.msg || "更新失败");
          }
        }).catch(error => {
          console.error("更新最新商品失败:", error);
          proxy.$modal.msgError("更新失败，请重试");
        });
      }).catch(() => {
        // 用户取消操作
      });
    } else {
      // 如果是新增模式，直接提交
      addCurrentProducts(newProductsForm.value.productIds).then(response => {
        if (response.code === 200) {
          proxy.$modal.msgSuccess("设置最新商品成功");
          newProductsOpen.value = false;
        } else {
          proxy.$modal.msgError(response.msg || "设置失败");
        }
      }).catch(error => {
        console.error("设置最新商品失败:", error);
        proxy.$modal.msgError("设置失败，请重试");
      });
    }
  }

  /** 取消最新商品 */
  function cancelNewProducts() {
    newProductsOpen.value = false;
    newProductsForm.value.productIds = [];
    selectedNewProducts.value = [];
    isNewProductsUpdateMode.value = false;
    existingNewProducts.value = [];
  }

  getList();

  // 新增：获取所有分类列表
  function getAllCategoriesList() {
    getAllCategories().then(response => {
      categoryOptions.value = response.data;
    });
  }

  // 在组件挂载时获取分类数据
  onMounted(() => {
    getList();
    getAllCategoriesList(); // 调用获取分类列表的方法
  });
</script>
<style>
.operation-buttons {
  display: flex;
  justify-content: center;
  gap: 5px;
}

.import-tips {
  margin-bottom: 15px;
  padding: 8px 16px;
  background-color: #f8f8f9;
  border-radius: 4px;
  border-left: 5px solid #ff9900;
}

.import-warning {
  font-weight: bold;
  color: #ff9900;
  margin-bottom: 8px;
}

.import-tips ul {
  padding-left: 20px;
  margin: 5px 0;
}

.import-tips li {
  line-height: 1.8;
}

.status-tag {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-success {
  background-color: #67C23A;
}

.status-warning {
  background-color: #E6A23C;
}

.status-danger {
  background-color: #F56C6C;
}

.status-info {
  background-color: #909399;
}

.status-default {
  background-color: #DCDFE6;
}

.selected-product-info {
  display: flex;
  gap: 20px;
  padding: 15px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #F8F9FA;
}

.product-details {
  flex: 1;
}

.product-details p {
  margin: 8px 0;
  line-height: 1.5;
}

.product-details strong {
  color: #606266;
  margin-right: 5px;
}

/* 最热商品样式 */
.selected-products-list {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
}

.selected-product-item {
  width: calc(50% - 10px);
  display: flex;
  align-items: center;
  padding: 10px;
  border: 1px solid #EBEEF5;
  border-radius: 4px;
  background-color: #F8F9FA;
}

.selected-product-item .product-details {
  margin-left: 10px;
}

.selected-product-item .product-name {
  font-weight: bold;
  margin-bottom: 5px;
  color: #303133;
}

.selected-product-item .product-price {
  color: #F56C6C;
  font-weight: bold;
}

@media (max-width: 768px) {
  .selected-product-item {
    width: 100%;
  }
}
</style>
