<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="100px">
                  <el-form-item label="品牌名称" prop="name">
                    <el-input
                        v-model="queryParams.name"
                        placeholder="请输入品牌名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="分类标识符" prop="slug">
                    <el-input
                        v-model="queryParams.slug"
                        placeholder="请输入分类标识符"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="所属分类" prop="categoryName">
                    <el-input
                        v-model="queryParams.categoryName"
                        placeholder="请输入所属分类"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="品牌入录时间" prop="createdAt">
                    <el-date-picker clearable
                                    v-model="queryParams.createdAt"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择品牌入录时间">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgBrands:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgBrands:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgBrands:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgBrands:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgBrandsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" type="index" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="品牌名称" align="center" prop="name" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="分类标识符" align="center" prop="slug" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="品牌Logo图片链接" align="center" prop="logoUrl" width="100" v-if="columns[3].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.logoUrl" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="品牌描述" align="center" prop="description" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="所属分类" align="center" prop="somgCategories.name" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                  <el-table-column label="品牌入录时间" align="center" prop="createdAt" width="180" v-if="columns[6].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgBrands:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgBrands:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg_品牌对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgBrandsRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="品牌名称" prop="name">
                          <el-input v-model="form.name" placeholder="请输入品牌名称" />
                        </el-form-item>
                        <el-form-item label="分类标识符" prop="slug">
                          <el-input v-model="form.slug" placeholder="请输入分类标识符" />
                        </el-form-item>
                        <el-form-item label="商品分类" prop="parentCategoryId">
                          <el-select 
                            v-model="form.parentCategoryId" 
                            placeholder="请选择商品分类"
                            :loading="categoryLoading"
                          >
                            <el-option
                              v-for="category in categoryOptions"
                              :key="category?.categoryId"
                              :label="category?.name"
                              :value="category?.categoryId"
                            />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="品牌Logo图片链接" prop="logoUrl">
                          <image-upload v-model="form.logoUrl"/>
                        </el-form-item>
                        <el-form-item label="品牌描述" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgBrands">
  import { listOmgBrands, getOmgBrands, delOmgBrands, addOmgBrands, updateOmgBrands } from "@/api/system/OmgBrands";
  import { listOmgCategories } from "@/api/system/OmgCategories";

  const { proxy } = getCurrentInstance();

  const OmgBrandsList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  const categoryOptions = ref([]); // 分类选项列表
  const categoryLoading = ref(false); // 分类加载状态

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
      name: null,
      slug: null,
      createdAt: null,
      categoryName: null,
    },
    rules: {
                    name: [
                { required: true, message: "品牌名称不能为空", trigger: "blur" }
              ],
                    slug: [
                { required: true, message: "分类标识符不能为空", trigger: "blur" }
              ],
                    description: [
                { required: true, message: "品牌描述不能为空", trigger: "blur" }
              ],
                    parentCategoryId: [
                { required: true, message: "商品分类不能为空", trigger: "change" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '品牌唯一标识', visible: true },
                { key: 1, label: '品牌名称', visible: true },
                { key: 2, label: '分类标识符', visible: true },
                { key: 3, label: '品牌Logo图片链接', visible: true },
                { key: 4, label: '品牌描述', visible: true },
                { key: 5, label: '所属分类', visible: true },
                { key: 6, label: '品牌入录时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg_品牌列表 */
  function getList() {
    loading.value = true;
    listOmgBrands(queryParams.value).then(response => {
            OmgBrandsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  /** 查询分类列表 */
  function getCategoryList() {
    categoryLoading.value = true;
    listOmgCategories({
      pageSize: 999 // 获取所有分类
    }).then(response => {
      categoryOptions.value = response.rows || [];
    }).finally(() => {
      categoryLoading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    brandId: null,
                    name: null,
                    slug: null,
                    logoUrl: null,
                    description: null,
                    parentCategoryId: null,
                    createdAt: null,
                    updatedAt: null
    };
    proxy.resetForm("OmgBrandsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.brandId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    getCategoryList(); // 获取分类列表
    open.value = true;
    title.value = "添加品牌";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _brandId = row.brandId || ids.value
    getCategoryList(); // 获取分类列表
    getOmgBrands(_brandId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改品牌";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgBrandsRef"].validate(valid => {
      if (valid) {
        if (form.value.brandId != null) {
          updateOmgBrands(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgBrands(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _brandIds = row.brandId || ids.value;
    proxy.$modal.confirm('是否确认删除omg_品牌编号为"' + _brandIds + '"的数据项？').then(function() {
      return delOmgBrands(_brandIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgBrands/export', {
      ...queryParams.value
    }, `OmgBrands_${new Date().getTime()}.xlsx`)
  }

  getList();
  getCategoryList(); // 初始加载分类列表
</script>
