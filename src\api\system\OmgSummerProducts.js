import request from '@/utils/request'

// 查询夏季新品列表
export function listOmgSummerProducts(query) {
  return request({
    url: '/system/OmgSummerProducts/list',
    method: 'get',
    params: query
  })
}

// 查询夏季新品详细
export function getOmgSummerProducts(id) {
  return request({
    url: '/system/OmgSummerProducts/' + id,
    method: 'get'
  })
}

// 新增夏季新品
export function addOmgSummerProducts(data) {
  return request({
    url: '/system/OmgSummerProducts',
    method: 'post',
    data: data
  })
}

// 修改夏季新品
export function updateOmgSummerProducts(data) {
  return request({
    url: '/system/OmgSummerProducts',
    method: 'put',
    data: data
  })
}

// 删除夏季新品
export function delOmgSummerProducts(id) {
  return request({
    url: '/system/OmgSummerProducts/' + id,
    method: 'delete'
  })
}
