import request from '@/utils/request'

// 查询omg追踪链接列表
export function listOmgLinks(query) {
  return request({
    url: '/system/OmgLinks/list',
    method: 'get',
    params: query
  })
}

// 查询omg追踪链接详细
export function getOmgLinks(linkId) {
  return request({
    url: '/system/OmgLinks/' + linkId,
    method: 'get'
  })
}

// 新增omg追踪链接
export function addOmgLinks(data) {
  return request({
    url: '/system/OmgLinks',
    method: 'post',
    data: data
  })
}

// 修改omg追踪链接
export function updateOmgLinks(data) {
  return request({
    url: '/system/OmgLinks',
    method: 'put',
    data: data
  })
}

// 删除omg追踪链接
export function delOmgLinks(linkId) {
  return request({
    url: '/system/OmgLinks/' + linkId,
    method: 'delete'
  })
}

// 批量生成链接
export function batchGenerateLinks(data) {
  return request({
    url: '/system/OmgLinks/batch',
    method: 'post',
    data: data
  })
}
