<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="120px">
                  <el-form-item label="自定义后缀" prop="customSuffix">
                    <el-input
                        v-model="queryParams.customSuffix"
                        placeholder="请输入自定义后缀"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="完整追踪链接" prop="fullTrackingUrl">
                    <el-input
                        v-model="queryParams.fullTrackingUrl"
                        placeholder="请输入完整追踪链接"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="短链接" prop="shortUrl">
                    <el-input
                        v-model="queryParams.shortUrl"
                        placeholder="请输入短链接"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="关联推广渠道" prop="channelName">
                    <el-input
                        v-model="queryParams.channelName"
                        placeholder="请输入关联推广渠道"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgLinks:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgLinks:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgLinks:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgLinks:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleBatchGenerate"
            v-hasPermi="['system:OmgLinks:add']"
        >批量生成链接</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgLinksList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="链接ID" align="center" prop="linkId" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="原始链接" align="center" prop="originalUrl" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="自定义后缀" align="center" prop="customSuffix" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="完整追踪链接" align="center" prop="fullTrackingUrl" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="短链接" align="center" prop="shortUrl" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="UTM参数" align="center" prop="utmParameters" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                  <el-table-column label="关联推广渠道" align="center" prop="channelName" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="点击次数" align="center" prop="clickCount" v-if="columns[7].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="创建时间" align="center" prop="createTime" width="180" v-if="columns[8].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="更新时间" align="center" prop="updateTime" width="180" v-if="columns[9].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.updateTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="链接状态 1启动 0禁用" align="center" prop="status" v-if="columns[10].visible" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgLinks:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgLinks:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg追踪链接对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgLinksRef" :model="form" :rules="rules" label-width="100px">
                        <el-form-item label="原始链接" prop="originalUrl">
                          <el-input v-model="form.originalUrl" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="自定义后缀" prop="customSuffix">
                          <el-input v-model="form.customSuffix" placeholder="请输入自定义后缀" />
                        </el-form-item>
                        <el-form-item label="完整追踪链接" prop="fullTrackingUrl">
                          <el-input v-model="form.fullTrackingUrl" placeholder="请输入完整追踪链接" />
                        </el-form-item>
                        <el-form-item label="短链接" prop="shortUrl">
                          <el-input v-model="form.shortUrl" placeholder="请输入短链接" />
                        </el-form-item>
                        <el-form-item label="UTM参数" prop="utmParameters">
                          <el-input v-model="form.utmParameters" placeholder="请输入UTM参数" />
                        </el-form-item>
                        <el-form-item label="关联推广渠道" prop="channelName">
                          <el-input v-model="form.channelName" placeholder="请输入关联推广渠道" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量生成链接对话框 -->
    <el-dialog title="批量生成链接" v-model="batchDialogVisible" width="600px" append-to-body>
      <el-form ref="batchFormRef" :model="batchForm" :rules="batchRules" label-width="140px">
        <el-form-item label="基础URL" prop="baseUrl">
          <el-input v-model="batchForm.baseUrl" placeholder="请输入基础URL，如https://www.example.com/products" />
        </el-form-item>
        <el-form-item label="生成数量" prop="count">
          <el-input-number v-model="batchForm.count" :min="1" :max="100" :precision="0" placeholder="请输入生成数量" />
        </el-form-item>
        <el-form-item label="自定义后缀模式" prop="customSuffixPattern">
          <el-input v-model="batchForm.customSuffixPattern" placeholder="请输入后缀模式，如promo_{number}_{date}" />
          <div class="el-form-item-tip">
            <small>支持的占位符: {number} - 序号, {date} - 日期, {random} - 随机字符串</small>
          </div>
        </el-form-item>
        <el-form-item label="UTM参数" prop="utmParameters">
          <el-input v-model="batchForm.utmParameters" placeholder="请输入UTM参数，如utm_source=email&utm_campaign=summer2023" />
        </el-form-item>
        <el-form-item label="关联推广渠道" prop="channelName">
          <el-input v-model="batchForm.channelName" placeholder="请输入关联的推广渠道" />
        </el-form-item>
        <el-form-item label="生成短链接" prop="generateShortUrl">
          <el-switch v-model="batchForm.generateShortUrl" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitBatchForm">确 定</el-button>
          <el-button @click="cancelBatchDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgLinks">
  import { listOmgLinks, getOmgLinks, delOmgLinks, addOmgLinks, updateOmgLinks, batchGenerateLinks } from "@/api/system/OmgLinks";
  import { ElMessage } from 'element-plus';

  const { proxy } = getCurrentInstance();

  const OmgLinksList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  
  // 批量生成链接相关变量
  const batchDialogVisible = ref(false);
  const batchForm = ref({
    baseUrl: "http://localhost:8083/client/",
    count: 10,
    customSuffixPattern: "promo_{number}_{date}",
    utmParameters: "utm_source=email&utm_campaign=summer2023",
    channelName: "电子邮件营销",
    generateShortUrl: true
  });
  
  const batchRules = {
    baseUrl: [
      { required: true, message: "基础URL不能为空", trigger: "blur" },
      { pattern: /^https?:\/\//, message: "请输入有效的URL，必须以http://或https://开头", trigger: "blur" }
    ],
    count: [
      { required: true, message: "生成数量不能为空", trigger: "blur" }
    ],
    customSuffixPattern: [
      { required: true, message: "自定义后缀模式不能为空", trigger: "blur" }
    ]
  };

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    originalUrl: null,
                    customSuffix: null,
                    fullTrackingUrl: null,
                    shortUrl: null,
                    channelName: null,
                    status: null
    },
    rules: {
    },
    //表格展示列
    columns: [
              { key: 0, label: '链接ID', visible: true },
                { key: 1, label: '原始链接', visible: true },
                { key: 2, label: '自定义后缀', visible: true },
                { key: 3, label: '完整追踪链接', visible: true },
                { key: 4, label: '短链接', visible: true },
                { key: 5, label: 'UTM参数', visible: true },
                { key: 6, label: '关联推广渠道', visible: true },
                { key: 7, label: '点击次数', visible: true },
                { key: 8, label: '创建时间', visible: true },
                { key: 9, label: '更新时间', visible: true },
                { key: 10, label: '链接状态 1启动 0禁用', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg追踪链接列表 */
  function getList() {
    loading.value = true;
    listOmgLinks(queryParams.value).then(response => {
            OmgLinksList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    linkId: null,
                    originalUrl: null,
                    customSuffix: null,
                    fullTrackingUrl: null,
                    shortUrl: null,
                    utmParameters: null,
                    sellerId: null,
                    channelName: null,
                    clickCount: null,
                    createTime: null,
                    updateTime: null,
                    status: null
    };
    proxy.resetForm("OmgLinksRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.linkId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加omg追踪链接";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _linkId = row.linkId || ids.value
    getOmgLinks(_linkId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改omg追踪链接";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgLinksRef"].validate(valid => {
      if (valid) {
        if (form.value.linkId != null) {
          updateOmgLinks(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgLinks(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _linkIds = row.linkId || ids.value;
    proxy.$modal.confirm('是否确认删除omg追踪链接编号为"' + _linkIds + '"的数据项？').then(function() {
      return delOmgLinks(_linkIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgLinks/export', {
      ...queryParams.value
    }, `OmgLinks_${new Date().getTime()}.xlsx`)
  }

  /** 批量生成链接按钮操作 */
  function handleBatchGenerate() {
    batchDialogVisible.value = true;
  }
  
  /** 取消批量生成链接对话框 */
  function cancelBatchDialog() {
    batchDialogVisible.value = false;
    // 重置表单
    batchForm.value = {
      baseUrl: "https://www.example.com/products",
      count: 10,
      customSuffixPattern: "promo_{number}_{date}",
      utmParameters: "utm_source=email&utm_campaign=summer2023",
      channelName: "电子邮件营销",
      generateShortUrl: true
    };
    proxy.resetForm("batchFormRef");
  }
  
  /** 提交批量生成链接表单 */
  function submitBatchForm() {
    proxy.$refs["batchFormRef"].validate(valid => {
      if (valid) {
        proxy.$modal.loading("正在生成链接，请稍候...");
        batchGenerateLinks(batchForm.value).then(response => {
          proxy.$modal.msgSuccess(`成功生成 ${batchForm.value.count} 个链接！`);
          batchDialogVisible.value = false;
          getList(); // 刷新列表以显示新生成的链接
        }).catch(() => {}).finally(() => {
          proxy.$modal.closeLoading();
        });
      }
    });
  }

  getList();
</script>

<style scoped>
.el-form-item-tip {
  font-size: 12px;
  color: #909399;
  line-height: 1.2;
  padding-top: 4px;
}
</style>
