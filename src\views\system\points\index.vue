<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="用户id" prop="userId">
                    <el-input
                        v-model="queryParams.userId"
                        placeholder="请输入用户id"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="更新时间" prop="updatedAt">
                    <el-date-picker clearable
                                    v-model="queryParams.updatedAt"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择更新时间">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:points:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:points:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:points:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:points:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="pointsList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="用户id" align="center" prop="userId" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="可用积分" align="center" prop="currentPoints" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="累计积分" align="center" prop="totalEarnedPoints" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <!-- <el-table-column label="积分id" align="center" prop="id" v-if="columns[3].visible" :show-overflow-tooltip="true"/> -->
                <el-table-column label="最后更新时间" align="center" prop="updatedAt" width="180" v-if="columns[4].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:points:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:points:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改用户积分对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="pointsRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="用户id" prop="userId">
                          <el-input v-model="form.userId" placeholder="请输入用户id" />
                        </el-form-item>
                        <el-form-item label="可用积分" prop="currentPoints">
                          <el-input v-model="form.currentPoints" placeholder="请输入可用积分" />
                        </el-form-item>
                        <el-form-item label="累计积分" prop="totalEarnedPoints">
                          <el-input v-model="form.totalEarnedPoints" placeholder="请输入累计积分" />
                        </el-form-item>
                        <el-form-item label="最后更新时间" prop="updatedAt">
                          <el-date-picker clearable
                                          v-model="form.updatedAt"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择最后更新时间">
                          </el-date-picker>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Points">
  import { listPoints, getPoints, delPoints, addPoints, updatePoints } from "@/api/system/points";

  const { proxy } = getCurrentInstance();

  const pointsList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    userId: null,
                    updatedAt: null
    },
    rules: {
    },
    //表格展示列
    columns: [
              { key: 0, label: '用户id', visible: true },
                { key: 1, label: '可用积分', visible: true },
                { key: 2, label: '累计积分', visible: true },
                { key: 3, label: '积分id', visible: true },
                { key: 4, label: '最后更新时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询用户积分列表 */
  function getList() {
    loading.value = true;
    listPoints(queryParams.value).then(response => {
            pointsList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    userId: null,
                    currentPoints: null,
                    totalEarnedPoints: null,
                    id: null,
                    updatedAt: null
    };
    proxy.resetForm("pointsRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加用户积分";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getPoints(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改用户积分";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["pointsRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updatePoints(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addPoints(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除用户积分编号为"' + _ids + '"的数据项？').then(function() {
      return delPoints(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/points/export', {
      ...queryParams.value
    }, `points_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
