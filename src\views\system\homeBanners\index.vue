<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="90px">
                  <el-form-item label="轮播图标题" prop="title">
                    <el-input
                        v-model="queryParams.title"
                        placeholder="请输入轮播图标题"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="轮播图状态" prop="status">
                    <el-select v-model="queryParams.status" style="width: 200px" placeholder="请选择轮播图状态" clearable>
                      <el-option
                          v-for="dict in banners_status"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:homeBanners:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:homeBanners:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:homeBanners:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:homeBanners:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="homeBannersList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="id" type="index" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="轮播图标题" align="center" prop="title" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="轮播图" align="center" prop="imageUrl" width="100" v-if="columns[2].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.imageUrl" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="点击跳转连接" align="center" prop="linkUrl" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="状态" align="center" prop="status" v-if="columns[4].visible">
                <template #default="scope">
                      <dict-tag :options="banners_status" :value="scope.row.status"/>
                </template>
              </el-table-column>
                <el-table-column label="显示顺序" align="center" prop="sortOrder" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="创建时间" align="center" prop="createdAt" width="180" v-if="columns[6].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="最后一次更新时间" align="center" prop="updatedAt" width="180" v-if="columns[7].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:homeBanners:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:homeBanners:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg首页轮播图对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="homeBannersRef" :model="form" :rules="rules" label-width="120px">
                        <el-form-item label="轮播图标题" prop="title">
                          <el-input v-model="form.title" placeholder="请输入轮播图标题" />
                        </el-form-item>
                        <el-form-item label="轮播图url" prop="imageUrl">
                          <image-upload v-model="form.imageUrl"/>
                        </el-form-item>
                        <el-form-item label="点击跳转连接" prop="linkUrl">
                          <el-input v-model="form.linkUrl" placeholder="请输入点击跳转连接" />
                        </el-form-item>
                        <el-form-item label="轮播图状态" prop="status">
                          <el-select v-model="form.status" placeholder="请选择轮播图状态">
                            <el-option
                                v-for="dict in banners_status"
                                :key="dict.value"
                                :label="dict.label"
                                :value="parseInt(dict.value)"
                            ></el-option>
                          </el-select>
                        </el-form-item>
                        <el-form-item label="显示顺序" prop="sortOrder">
                          <el-input v-model="form.sortOrder" placeholder="请输入显示顺序" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="HomeBanners">
  import { listHomeBanners, getHomeBanners, delHomeBanners, addHomeBanners, updateHomeBanners } from "@/api/system/homeBanners";

  const { proxy } = getCurrentInstance();
      const { banners_status } = proxy.useDict('banners_status');

  const homeBannersList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    title: null,
                    status: null,
    },
    rules: {
                    title: [
                { required: true, message: "轮播图标题(可选)不能为空", trigger: "blur" }
              ],
                    imageUrl: [
                { required: true, message: "轮播图url不能为空", trigger: "blur" }
              ],
                    linkUrl: [
                { required: true, message: "点击跳转连接不能为空", trigger: "blur" }
              ],
                    status: [
                { required: true, message: "轮播图状态是否启用,0为禁用,1为启用不能为空", trigger: "change" }
              ],
                    sortOrder: [
                { required: true, message: "显示顺序不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '首页轮播图id', visible: true },
                { key: 1, label: '轮播图标题', visible: true },
                { key: 2, label: '轮播图url', visible: true },
                { key: 3, label: '点击跳转连接', visible: true },
                { key: 4, label: '轮播图状态', visible: true },
                { key: 5, label: '显示顺序', visible: true },
                { key: 6, label: '创建时间', visible: true },
                { key: 7, label: '最后一次更新时间', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg首页轮播图列表 */
  function getList() {
    loading.value = true;
    listHomeBanners(queryParams.value).then(response => {
            homeBannersList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    title: null,
                    imageUrl: null,
                    linkUrl: null,
                    status: null,
                    sortOrder: null,
                    createdAt: null,
                    updatedAt: null
    };
    proxy.resetForm("homeBannersRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加omg首页轮播图";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getHomeBanners(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改omg首页轮播图";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["homeBannersRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateHomeBanners(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addHomeBanners(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除omg首页轮播图编号为"' + _ids + '"的数据项？').then(function() {
      return delHomeBanners(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/homeBanners/export', {
      ...queryParams.value
    }, `homeBanners_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
