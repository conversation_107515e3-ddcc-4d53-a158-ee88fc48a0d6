import request from '@/utils/request'

// 查询omg_品牌列表
export function listOmgBrands(query) {
  return request({
    url: '/system/OmgBrands/list',
    method: 'get',
    params: query
  })
}

// 查询omg_品牌详细
export function getOmgBrands(brandId) {
  return request({
    url: '/system/OmgBrands/' + brandId,
    method: 'get'
  })
}

// 新增omg_品牌
export function addOmgBrands(data) {
  return request({
    url: '/system/OmgBrands',
    method: 'post',
    data: data
  })
}

// 修改omg_品牌
export function updateOmgBrands(data) {
  return request({
    url: '/system/OmgBrands',
    method: 'put',
    data: data
  })
}

// 删除omg_品牌
export function delOmgBrands(brandId) {
  return request({
    url: '/system/OmgBrands/' + brandId,
    method: 'delete'
  })
}

// 根据分类ID获取品牌列表
export function getBrandListByCategoryId(categoryId) {
  return request({
    url: '/system/OmgBrands/getBrandListByCategoryId',
    method: 'get',
    params: { categoryId }
  })
}
