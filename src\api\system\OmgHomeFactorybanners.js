import request from '@/utils/request'

// 查询omg首页工厂图片列表
export function listOmgHomeFactorybanners(query) {
  return request({
    url: '/system/OmgHomeFactorybanners/list',
    method: 'get',
    params: query
  })
}

// 查询omg首页工厂图片详细
export function getOmgHomeFactorybanners(id) {
  return request({
    url: '/system/OmgHomeFactorybanners/' + id,
    method: 'get'
  })
}

// 新增omg首页工厂图片
export function addOmgHomeFactorybanners(data) {
  return request({
    url: '/system/OmgHomeFactorybanners',
    method: 'post',
    data: data
  })
}

// 修改omg首页工厂图片
export function updateOmgHomeFactorybanners(data) {
  return request({
    url: '/system/OmgHomeFactorybanners',
    method: 'put',
    data: data
  })
}

// 删除omg首页工厂图片
export function delOmgHomeFactorybanners(id) {
  return request({
    url: '/system/OmgHomeFactorybanners/' + id,
    method: 'delete'
  })
}
