<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="邀请码" prop="inviteCode">
                    <el-input
                        v-model="queryParams.inviteCode"
                        placeholder="请输入邀请码"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="注册的用户ID" prop="userId">
                    <el-input
                        v-model="queryParams.userId"
                        placeholder="请输入注册的用户ID"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="注册时间" prop="registerTime">
                    <el-date-picker clearable
                                    v-model="queryParams.registerTime"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择注册时间">
                    </el-date-picker>
                  </el-form-item>
                  <el-form-item label="注册日期" prop="registerDate">
                    <el-date-picker clearable
                                    v-model="queryParams.registerDate"
                                    type="date"
                                    value-format="YYYY-MM-DD"
                                    placeholder="请选择注册日期">
                    </el-date-picker>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:registration:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:registration:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:registration:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:registration:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="registrationList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="id" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="邀请码" align="center" prop="inviteCode" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="注册的用户ID" align="center" prop="userId" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="注册时间" align="center" prop="registerTime" width="180" v-if="columns[3].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.registerTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="注册日期" align="center" prop="registerDate" width="180" v-if="columns[4].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.registerDate, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:registration:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:registration:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改邀请码注册记录对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="registrationRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="邀请码" prop="inviteCode">
                          <el-input v-model="form.inviteCode" placeholder="请输入邀请码" />
                        </el-form-item>
                        <el-form-item label="注册的用户ID" prop="userId">
                          <el-input v-model="form.userId" placeholder="请输入注册的用户ID" />
                        </el-form-item>
                        <el-form-item label="注册时间" prop="registerTime">
                          <el-date-picker clearable
                                          v-model="form.registerTime"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择注册时间">
                          </el-date-picker>
                        </el-form-item>
                        <el-form-item label="注册日期" prop="registerDate">
                          <el-date-picker clearable
                                          v-model="form.registerDate"
                                          type="date"
                                          value-format="YYYY-MM-DD"
                                          placeholder="请选择注册日期">
                          </el-date-picker>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Registration">
  import { listRegistration, getRegistration, delRegistration, addRegistration, updateRegistration } from "@/api/system/registration";

  const { proxy } = getCurrentInstance();

  const registrationList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    inviteCode: null,
                    userId: null,
                    registerTime: null,
                    registerDate: null
    },
    rules: {
                    inviteCode: [
                { required: true, message: "邀请码不能为空", trigger: "blur" }
              ],
                    userId: [
                { required: true, message: "注册的用户ID不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: 'id', visible: true },
                { key: 1, label: '邀请码', visible: true },
                { key: 2, label: '注册的用户ID', visible: true },
                { key: 3, label: '注册时间', visible: true },
                { key: 4, label: '注册日期', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询邀请码注册记录列表 */
  function getList() {
    loading.value = true;
    listRegistration(queryParams.value).then(response => {
            registrationList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    inviteCode: null,
                    userId: null,
                    registerTime: null,
                    registerDate: null
    };
    proxy.resetForm("registrationRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加邀请码注册记录";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getRegistration(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改邀请码注册记录";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["registrationRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateRegistration(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addRegistration(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除邀请码注册记录编号为"' + _ids + '"的数据项？').then(function() {
      return delRegistration(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/registration/export', {
      ...queryParams.value
    }, `registration_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
