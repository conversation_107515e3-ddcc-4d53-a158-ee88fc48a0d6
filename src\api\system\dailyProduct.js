import request from '@/utils/request'

// 查询每日一品列表
export function listDailyProduct(query) {
  return request({
    url: '/system/dailyProduct/list',
    method: 'get',
    params: query
  })
}

// 查询每日一品详细
export function getDailyProduct(id) {
  return request({
    url: '/system/dailyProduct/' + id,
    method: 'get'
  })
}

// 新增每日一品
export function addDailyProduct(data) {
  return request({
    url: '/system/dailyProduct',
    method: 'post',
    data: data
  })
}

// 修改每日一品
export function updateDailyProduct(data) {
  return request({
    url: '/system/dailyProduct',
    method: 'put',
    data: data
  })
}

// 删除每日一品
export function delDailyProduct(id) {
  return request({
    url: '/system/dailyProduct/' + id,
    method: 'delete'
  })
}
