import request from '@/utils/request'

// 查询商家管理列表
export function listSellers(query) {
  return request({
    url: '/system/sellers/list',
    method: 'get',
    params: query
  })
}

// 查询商家管理详细
export function getSellers(sellerId) {
  return request({
    url: '/system/sellers/' + sellerId,
    method: 'get'
  })
}

// 新增商家管理
export function addSellers(data) {
  return request({
    url: '/system/sellers',
    method: 'post',
    data: data
  })
}

// 修改商家管理
export function updateSellers(data) {
  return request({
    url: '/system/sellers',
    method: 'put',
    data: data
  })
}

// 删除商家管理
export function delSellers(sellerId) {
  return request({
    url: '/system/sellers/' + sellerId,
    method: 'delete'
  })
}

// 批量推荐商家(带排序)
export function recommendSellers(recommendData) {
  return request({
    url: '/system/sellers/recommend',
    method: 'post',
    data: recommendData
  })
}

// 获取已推荐商家列表
export function getRecommendedSellers() {
  return request({
    url: '/system/sellers/recommend/list',
    method: 'get'
  })
}
