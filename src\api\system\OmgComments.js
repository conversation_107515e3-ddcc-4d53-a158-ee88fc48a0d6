import request from '@/utils/request'

// 查询帖子评论列表
export function listOmgComments(query) {
  return request({
    url: '/system/OmgComments/list',
    method: 'get',
    params: query
  })
}

// 查询帖子评论详细
export function getOmgComments(id) {
  return request({
    url: '/system/OmgComments/' + id,
    method: 'get'
  })
}

// 新增帖子评论
export function addOmgComments(data) {
  return request({
    url: '/system/OmgComments',
    method: 'post',
    data: data
  })
}

// 修改帖子评论
export function updateOmgComments(data) {
  return request({
    url: '/system/OmgComments',
    method: 'put',
    data: data
  })
}

// 删除帖子评论
export function delOmgComments(id) {
  return request({
    url: '/system/OmgComments/' + id,
    method: 'delete'
  })
}
