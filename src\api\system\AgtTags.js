import request from '@/utils/request'

// 查询标签列表
export function listAgtTags(query) {
  return request({
    url: '/system/AgtTags/list',
    method: 'get',
    params: query
  })
}

// 查询标签详细
export function getAgtTags(id) {
  return request({
    url: '/system/AgtTags/' + id,
    method: 'get'
  })
}

// 新增标签
export function addAgtTags(data) {
  return request({
    url: '/system/AgtTags',
    method: 'post',
    data: data
  })
}

// 修改标签
export function updateAgtTags(data) {
  return request({
    url: '/system/AgtTags',
    method: 'put',
    data: data
  })
}

// 删除标签
export function delAgtTags(id) {
  return request({
    url: '/system/AgtTags/' + id,
    method: 'delete'
  })
}
