<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="评论状态" prop="status">
                    <el-select v-model="queryParams.status" style="width: 200px" placeholder="请选择评论状态" clearable>
                      <el-option
                          v-for="dict in omg_comment_status"
                          :key="dict.value"
                          :label="dict.label"
                          :value="dict.value"
                      />
                    </el-select>
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="showAddDialog"
            v-hasPermi="['system:OmgComment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgComment:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgComment:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgComment:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Upload"
            @click="handleGenerateData"
            v-hasPermi="['system:OmgComment:add']"
        >批量导入评论</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgCommentList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="序号" align="center" prop="commentId" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="关联商品ID" align="center" prop="productId" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户名称" align="center" prop="users.username" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                  <el-table-column label="评论内容" align="center" prop="content" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="点赞数" align="center" prop="likes" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="评论图片" align="center" prop="images" v-if="columns[5].visible">
                  <template #default="scope">
                    <div v-if="parseImages(scope.row.images).length" class="comment-images">
                      <el-carousel :interval="3000" arrow="always" height="160px" style="width: 240px;">
                        <el-carousel-item v-for="(img, index) in parseImages(scope.row.images)" :key="index" class="carousel-img-item">
                          <el-image
                            :src="img"
                            style="width: 220px; height: 160px; object-fit: contain; background: #fff;"
                            :preview-src-list="parseImages(scope.row.images)"
                            fit="contain"
                          />
                        </el-carousel-item>
                      </el-carousel>
                    </div>
                    <span v-else>无图片</span>
                  </template>
                </el-table-column>
                <el-table-column label="评论创建时间" align="center" prop="createdAt" width="180" v-if="columns[6].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="评论最后更新时间" align="center" prop="updatedAt" width="180" v-if="columns[7].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.updatedAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="评论状态" align="center" prop="status" v-if="columns[8].visible">
                <template #default="scope">
                      <dict-tag :options="omg_comment_status" :value="scope.row.status">
                        <template #default="{ label, value }">
                          <div class="status-tag">
                            <span class="status-dot" :class="isDisabled(label, value) ? 'status-dot-red' : 'status-dot-green'"></span>
                            {{ label }}
                          </div>
                        </template>
                      </dict-tag>
                </template>
              </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgComment:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgComment:remove']">删除</el-button>
          <!-- <el-button link type="primary" icon="ChatLineRound" @click="handleReply(scope.row)" v-hasPermi="['system:OmgComment:add']">回复</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg_评论对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgCommentRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="商品ID" prop="productId">
                          <el-input v-model="form.productId" placeholder="请输入关联商品ID" />
                        </el-form-item>
                        <el-form-item label="父评论ID" prop="parentCommentId">
                          <el-input v-model="form.parentCommentId" placeholder="请输入父评论ID" />
                        </el-form-item>
                        <el-form-item label="评论内容">
                          <editor v-model="form.content" :min-height="192"/>
                        </el-form-item>
                        <el-form-item label="点赞数" prop="likes">
                          <el-input v-model="form.likes" placeholder="请输入点赞数" />
                        </el-form-item>
                        <el-form-item label="评论图片" prop="images">
                          <!-- 图片上传组件 -->
                          <el-upload
                            action="/dev-api/common/upload"
                            list-type="picture-card"
                            :headers="headers"
                            :file-list="fileList"
                            :on-preview="handlePictureCardPreview"
                            :on-remove="handleRemove"
                            :on-success="handleUploadSuccess"
                            :before-upload="beforeUpload"
                            multiple
                          >
                            <el-icon><Plus /></el-icon>
                          </el-upload>
                          <el-dialog v-model="dialogVisible" title="预览图片">
                            <img w-full :src="dialogImageUrl" alt="Preview Image" style="max-width: 100%; max-height: 100%;" />
                          </el-dialog>
                          <div v-if="fileList.length > 0" class="image-tip">
                            <el-alert type="info" :closable="false">
                              提示：点击图片右上角的删除图标可以移除已上传的图片
                            </el-alert>
                          </div>
                        </el-form-item>
                        <el-form-item label="评论状态" prop="status">
                          <el-select v-model="form.status" placeholder="请选择评论状态">
                            <el-option
                                v-for="dict in omg_comment_status"
                                :key="dict.value"
                                :label="dict.label"
                                :value="dict.value"
                            ></el-option>
                          </el-select>
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 批量生成评论弹窗 -->
    <el-dialog title="批量生成评论测试数据" v-model="generateDialogVisible" width="500px" append-to-body>
      <el-form ref="generateFormRef" :model="generateForm" :rules="generateRules" label-width="120px">
        <el-form-item label="用户数量" prop="userCount">
          <el-input-number v-model="generateForm.userCount" :min="1" :max="100" placeholder="请输入要创建的用户数量" />
        </el-form-item>
        <el-form-item label="评论数量" prop="commentCount">
          <el-input-number v-model="generateForm.commentCount" :min="1" :max="1000" placeholder="请输入要创建的评论数量" />
        </el-form-item>
        <el-form-item label="商品ID" prop="productId">
          <el-select 
            v-model="generateForm.productId" 
            filterable 
            placeholder="请选择商品SKU" 
            style="width: 100%">
            <el-option
              v-for="item in productOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="评论内容模板" prop="commentContents">
          <div v-for="(item, index) in generateForm.commentContents" :key="index" style="display: flex; margin-bottom: 10px;">
            <el-input 
              v-model="generateForm.commentContents[index]" 
              placeholder="请输入评论内容"
              style="flex: 1; margin-right: 10px;"
            />
            <el-button 
              type="danger" 
              icon="Delete" 
              circle 
              @click="removeCommentContent(index)"
              :disabled="generateForm.commentContents.length <= 1"
            ></el-button>
          </div>
          <el-button type="primary" icon="Plus" @click="addCommentContent">添加评论内容</el-button>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitGenerateForm">确 定</el-button>
          <el-button @click="cancelGenerateDialog">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgComment">
  import { listOmgComment, getOmgComment, delOmgComment, addOmgComment, updateOmgComment, generateCommentTestData } from "@/api/system/OmgComment";
  import { listOmgProducts } from "@/api/system/OmgProducts";
  import { ElMessage } from 'element-plus';
  import { Plus, ChatLineRound, Upload } from '@element-plus/icons-vue';
  import { getToken } from "@/utils/auth";

  const { proxy } = getCurrentInstance();
      const { omg_comment_status } = proxy.useDict('omg_comment_status');

  const OmgCommentList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");
  
  // 图片上传相关变量
  const fileList = ref([]);
  const dialogImageUrl = ref('');
  const dialogVisible = ref(false);
  const headers = ref({ Authorization: 'Bearer ' + getToken() });
  const uploadedImages = ref([]);
  
  // 商品列表
  const productOptions = ref([]);

  // 批量生成评论弹窗相关变量
  const generateDialogVisible = ref(false);
  const generateForm = ref({
    userCount: 5,
    commentCount: 10,
    productId: '',
    commentContents: ["质量很好，值得推荐！", "物美价廉，物流很快"]
  });
  const generateRules = {
    userCount: [{ required: true, message: "用户数量不能为空", trigger: "blur" }],
    commentCount: [{ required: true, message: "评论数量不能为空", trigger: "blur" }],
    productId: [{ required: true, message: "商品ID不能为空", trigger: "blur" }]
  };

  // 解析评论图片JSON字符串为数组
  const parseImages = (imagesStr) => {
    if (!imagesStr) return [];
    try {
      // 先尝试 JSON 解析
      const arr = JSON.parse(imagesStr);
      if (Array.isArray(arr)) return arr;
      // 如果解析出来不是数组，尝试逗号分隔
      if (typeof arr === 'string') return arr.split(',').map(s => s.trim()).filter(Boolean);
      return [];
    } catch (e) {
      // 不是 JSON，尝试逗号分隔
      if (typeof imagesStr === 'string') {
        return imagesStr.split(',').map(s => s.trim()).filter(Boolean);
      }
      return [];
    }
  };

  // 图片上传前的钩子
  const beforeUpload = (file) => {
    const isJPG = file.type === 'image/jpeg';
    const isPNG = file.type === 'image/png';
    const isLt2M = file.size / 1024 / 1024 < 5;

    if (!isJPG && !isPNG) {
      ElMessage.error('上传图片只能是 JPG 或 PNG 格式!');
      return false;
    }
    if (!isLt2M) {
      ElMessage.error('上传图片大小不能超过 5MB!');
      return false;
    }
    return true;
  };

  // 处理图片上传成功
  const handleUploadSuccess = (response, file, fileList) => {
    if (response.code === 200) {
      uploadedImages.value.push(response.url);
      
      // 更新form.images字段
      form.value.images = JSON.stringify(uploadedImages.value);
    } else {
      ElMessage.error('图片上传失败');
    }
  };

  // 移除图片
  const handleRemove = (file, fileList) => {
    // 从uploadedImages中找到对应的URL并移除
    const fileUrl = file.response?.url || file.url; // 处理新上传和已有的图片
    if (fileUrl) {
      const index = uploadedImages.value.findIndex(url => url === fileUrl);
      if (index !== -1) {
        uploadedImages.value.splice(index, 1);
        form.value.images = uploadedImages.value.length ? JSON.stringify(uploadedImages.value) : null;
      }
    }
  };

  // 点击预览图片
  const handlePictureCardPreview = (file) => {
    dialogImageUrl.value = file.url || file.response?.url;
    dialogVisible.value = true;
  };

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    productId: null,
                    userId: null,
                    status: null
    },
    rules: {
                    productId: [
                { required: true, message: "关联商品ID不能为空", trigger: "change" }
              ],
                    userId: [
                { required: true, message: "评论用户ID不能为空", trigger: "change" }
              ],
                    content: [
                { required: true, message: "评论内容不能为空", trigger: "blur" }
              ],
                    likes: [
                { required: true, message: "点赞数不能为空", trigger: "blur" }
              ],
                    status: [
                { required: true, message: "评论状态不能为空", trigger: "change" }
              ]
    },
    //表格展示列
    columns: [
              { key: 0, label: '序号', visible: true },
                { key: 1, label: '关联商品ID', visible: true },
                { key: 2, label: '用户名称', visible: true },
                { key: 3, label: '评论内容', visible: true },
                { key: 4, label: '点赞数', visible: true },
                { key: 5, label: '评论图片', visible: true },
                { key: 6, label: '评论创建时间', visible: true },
                { key: 7, label: '评论最后更新时间', visible: true },
                { key: 8, label: '评论状态', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg_评论列表 */
  function getList() {
    loading.value = true;
    listOmgComment(queryParams.value).then(response => {
            OmgCommentList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    commentId: null,
                    productId: null,
                    userId: null,
                    parentCommentId: null,
                    content: null,
                    likes: null,
                    createdAt: null,
                    updatedAt: null,
                    status: null,
                    images: null
    };
    fileList.value = [];
    uploadedImages.value = [];
    proxy.resetForm("OmgCommentRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.commentId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd(productId, commentId) {
    reset();
    if (productId) {
      form.value.productId = productId;
    }
    if (commentId) {
      form.value.parentCommentId = commentId; // 将传入的commentId设为父评论ID
    }
    open.value = true;
    title.value = "添加omg_评论";
  }
  
  /** 显示新增对话框并选择商品 */
  function showAddDialog() {
    // 调用handleAdd并传入可能的预设商品ID
    handleAdd();
  }

  /** 回复评论操作 */
  function handleReply(row) {
    reset();
    // 设置商品ID和父评论ID
    form.value.productId = row.productId;
    form.value.parentCommentId = row.commentId;
    open.value = true;
    title.value = `回复评论 (${row.users?.username || '用户'})`;
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _commentId = row.commentId || ids.value
    getOmgComment(_commentId).then(response => {
      form.value = response.data;
      // 如果有图片，初始化已上传图片列表
      if (form.value.images) {
        try {
          const imageUrls = parseImages(form.value.images);
          uploadedImages.value = [...imageUrls];
          
          // 构建fileList用于el-upload展示
          fileList.value = imageUrls.map((url, index) => ({
            name: `已上传图片${index + 1}`,
            url: url,
          }));
        } catch (error) {
          console.error('解析图片数据失败:', error);
        }
      }
      open.value = true;
      title.value = "修改omg_评论";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgCommentRef"].validate(valid => {
      if (valid) {
        if (form.value.commentId != null) {
          updateOmgComment(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgComment(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _commentIds = row.commentId || ids.value;
    proxy.$modal.confirm('是否确认删除omg_评论编号为"' + _commentIds + '"的数据项？').then(function() {
      return delOmgComment(_commentIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgComment/export', {
      ...queryParams.value
    }, `OmgComment_${new Date().getTime()}.xlsx`)
  }

  /** 显示批量生成评论弹窗 */
  function handleGenerateData() {
    generateDialogVisible.value = true;
  }

  /** 取消批量生成评论弹窗 */
  function cancelGenerateDialog() {
    generateDialogVisible.value = false;
    generateForm.value = {
      userCount: 5,
      commentCount: 10,
      productId: '',
      commentContents: ["质量很好，值得推荐！", "物美价廉，物流很快"]
    };
    proxy.resetForm("generateFormRef");
  }

  /** 添加评论内容 */
  function addCommentContent() {
    generateForm.value.commentContents.push("");
  }

  /** 删除评论内容 */
  function removeCommentContent(index) {
    if (generateForm.value.commentContents.length > 1) {
      generateForm.value.commentContents.splice(index, 1);
    } else {
      proxy.$modal.msgError("至少需要保留一条评论内容");
    }
  }

  /** 提交批量生成评论表单 */
  function submitGenerateForm() {
    proxy.$refs["generateFormRef"]?.validate(valid => {
      if (valid) {
        // 检查评论内容是否为空
        const emptyContentIndex = generateForm.value.commentContents.findIndex(content => !content.trim());
        if (emptyContentIndex !== -1) {
          proxy.$modal.msgError(`第 ${emptyContentIndex + 1} 条评论内容不能为空`);
          return;
        }
        
        proxy.$modal.loading("正在生成评论数据，请稍候...");
        generateCommentTestData(generateForm.value).then(response => {
          proxy.$modal.msgSuccess("评论数据生成成功");
          generateDialogVisible.value = false;
          getList();
        }).catch(() => {}).finally(() => {
          proxy.$modal.closeLoading();
        });
      }
    });
  }

  // 判断是否为禁用状态
  const isDisabled = (label, value) => {
    return label.includes('禁用') || value === '0' || value === 0;
  };
  
  // 获取商品列表
  function getProductList() {
    listOmgProducts({
      pageNum: 1,
      pageSize: 1000 // 设置较大的数值以便一次获取更多商品
    }).then(response => {
      productOptions.value = response.rows.map(item => {
        return {
          value: item.productId,
          label: `${item.sku || ''} - ${item.name || ''}`
        };
      });
    });
  }

  // 添加一个函数来检查字典数据
  onMounted(() => {
    console.log('评论状态字典数据:', omg_comment_status.value);
    getProductList(); // 加载商品列表
  });

  getList();
</script>

<style scoped>
.image-preview {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 15px;
}

.comment-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.el-upload--picture-card {
  --el-upload-picture-card-size: 100px;
}

.el-upload-list--picture-card .el-upload-list__item {
  width: 100px;
  height: 100px;
}

.image-tip {
  margin-top: 10px;
}

/* 添加状态点的样式 */
.status-tag {
  display: inline-flex;
  align-items: center;
  height: 100%;
  line-height: 1;
  vertical-align: middle;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-right: 5px;
  flex-shrink: 0;
  vertical-align: middle;
}

.status-dot-green {
  background-color: #67c23a;
}

.status-dot-red {
  background-color: #f56c6c;
}

.carousel-img-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 160px;
}
</style>
