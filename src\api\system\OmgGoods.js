import request from '@/utils/request'

// 查询积分商城列表
export function listOmgGoods(query) {
  return request({
    url: '/system/OmgGoods/list',
    method: 'get',
    params: query
  })
}

// 查询积分商城详细
export function getOmgGoods(id) {
  return request({
    url: '/system/OmgGoods/' + id,
    method: 'get'
  })
}

// 新增积分商城
export function addOmgGoods(data) {
  return request({
    url: '/system/OmgGoods',
    method: 'post',
    data: data
  })
}

// 修改积分商城
export function updateOmgGoods(data) {
  return request({
    url: '/system/OmgGoods',
    method: 'put',
    data: data
  })
}

// 删除积分商城
export function delOmgGoods(id) {
  return request({
    url: '/system/OmgGoods/' + id,
    method: 'delete'
  })
}
