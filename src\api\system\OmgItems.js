import request from '@/utils/request'

// 查询omg 首页qc图列表
export function listOmgItems(query) {
  return request({
    url: '/system/OmgItems/list',
    method: 'get',
    params: query
  })
}

// 查询omg 首页qc图详细
export function getOmgItems(id) {
  return request({
    url: '/system/OmgItems/' + id,
    method: 'get'
  })
}

// 新增omg 首页qc图
export function addOmgItems(data) {
  return request({
    url: '/system/OmgItems',
    method: 'post',
    data: data
  })
}

// 修改omg 首页qc图
export function updateOmgItems(data) {
  return request({
    url: '/system/OmgItems',
    method: 'put',
    data: data
  })
}

// 删除omg 首页qc图
export function delOmgItems(id) {
  return request({
    url: '/system/OmgItems/' + id,
    method: 'delete'
  })
}
