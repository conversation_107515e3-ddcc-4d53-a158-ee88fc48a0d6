<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="头像" prop="avatar">
                    <el-input
                        v-model="queryParams.avatar"
                        placeholder="请输入头像"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="名称" prop="username">
                    <el-input
                        v-model="queryParams.username"
                        placeholder="请输入名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="内容" prop="description">
                    <el-input
                        v-model="queryParams.description"
                        placeholder="请输入内容"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:OmgHomeVideo:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:OmgHomeVideo:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:OmgHomeVideo:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:OmgHomeVideo:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="OmgHomeVideoList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <el-table-column label="达人视频表id" align="center" prop="id" v-if="columns[0].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="视频URL" align="center" prop="videoUrl" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="头像" align="center" prop="avatar" v-if="columns[2].visible" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <image-preview :src="scope.row.avatar" :width="50" :height="50" />
                  </template>
                </el-table-column>
                <el-table-column label="名称" align="center" prop="username" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="内容" align="center" prop="description" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:OmgHomeVideo:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:OmgHomeVideo:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改omg首页视频组对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="OmgHomeVideoRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="视频文件" prop="videoUrl">
                          <file-upload 
                            v-model="form.videoUrl" 
                            :limit="1" 
                            :file-type="['mp4', 'mov', 'avi', 'flv', 'wmv']"
                            :file-size="50"
                          />
                        </el-form-item>
                        <el-form-item label="头像" prop="avatar">
                          <image-upload v-model="form.avatar" :limit="1" />
                        </el-form-item>
                        <el-form-item label="名称" prop="username">
                          <el-input v-model="form.username" placeholder="请输入名称" />
                        </el-form-item>
                        <el-form-item label="内容" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="OmgHomeVideo">
  import { listOmgHomeVideo, getOmgHomeVideo, delOmgHomeVideo, addOmgHomeVideo, updateOmgHomeVideo } from "@/api/system/OmgHomeVideo";
  // Import file upload and image upload components
  import FileUpload from "@/components/FileUpload/index.vue";
  import ImageUpload from "@/components/ImageUpload/index.vue";
  import ImagePreview from "@/components/ImagePreview/index.vue";

  const { proxy } = getCurrentInstance();

  const OmgHomeVideoList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    videoUrl: null,
                    avatar: null,
                    username: null,
                    description: null
    },
    rules: {
      videoUrl: [
        { required: true, message: "视频文件不能为空", trigger: "change" }
      ],
      avatar: [
        { required: true, message: "头像不能为空", trigger: "change" }
      ],
      username: [
        { required: true, message: "名称不能为空", trigger: "blur" }
      ],
      description: [
        { required: true, message: "内容不能为空", trigger: "blur" }
      ]
    },
    //表格展示列
    columns: [
              { key: 0, label: '达人视频表id', visible: true },
                { key: 1, label: '视频URL', visible: true },
                { key: 2, label: '头像', visible: true },
                { key: 3, label: '名称', visible: true },
                { key: 4, label: '内容', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询omg首页视频组列表 */
  function getList() {
    loading.value = true;
    listOmgHomeVideo(queryParams.value).then(response => {
            OmgHomeVideoList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    id: null,
                    videoUrl: null,
                    avatar: null,
                    username: null,
                    description: null
    };
    proxy.resetForm("OmgHomeVideoRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.id);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加omg首页视频组";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _id = row.id || ids.value
    getOmgHomeVideo(_id).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改omg首页视频组";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["OmgHomeVideoRef"].validate(valid => {
      if (valid) {
        if (form.value.id != null) {
          updateOmgHomeVideo(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addOmgHomeVideo(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _ids = row.id || ids.value;
    proxy.$modal.confirm('是否确认删除omg首页视频组编号为"' + _ids + '"的数据项？').then(function() {
      return delOmgHomeVideo(_ids);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/OmgHomeVideo/export', {
      ...queryParams.value
    }, `OmgHomeVideo_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>

<style>
.video-preview {
  max-width: 300px;
  margin: 10px 0;
}
</style>
