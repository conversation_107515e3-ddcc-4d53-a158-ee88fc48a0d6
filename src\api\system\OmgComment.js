import request from '@/utils/request'

// 查询omg_评论列表
export function listOmgComment(query) {
  return request({
    url: '/system/OmgComment/list',
    method: 'get',
    params: query
  })
}

// 查询omg_评论详细
export function getOmgComment(commentId) {
  return request({
    url: '/system/OmgComment/' + commentId,
    method: 'get'
  })
}

// 新增omg_评论
export function addOmgComment(data) {
  return request({
    url: '/system/OmgComment',
    method: 'post',
    data: data
  })
}

// 修改omg_评论
export function updateOmgComment(data) {
  return request({
    url: '/system/OmgComment',
    method: 'put',
    data: data
  })
}

// 删除omg_评论
export function delOmgComment(commentId) {
  return request({
    url: '/system/OmgComment/' + commentId,
    method: 'delete'
  })
}

// 批量生成评论测试数据
export function generateCommentTestData(data) {
  return request({
    url: '/system/OmgComment/testData/generate',
    method: 'post',
    data: data
  })
}
