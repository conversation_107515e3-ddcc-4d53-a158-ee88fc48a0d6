import request from '@/utils/request'

// 查询最热商品列表
export function listHotproduct(query) {
  return request({
    url: '/system/Hotproduct/list',
    method: 'get',
    params: query
  })
}

// 查询最热商品详细
export function getHotproduct(id) {
  return request({
    url: '/system/Hotproduct/' + id,
    method: 'get'
  })
}

// 新增最热商品
export function addHotproduct(data) {
  return request({
    url: '/system/Hotproduct',
    method: 'post',
    data: data
  })
}

// 修改最热商品
export function updateHotproduct(data) {
  return request({
    url: '/system/Hotproduct',
    method: 'put',
    data: data
  })
}

// 删除最热商品
export function delHotproduct(id) {
  return request({
    url: '/system/Hotproduct/' + id,
    method: 'delete'
  })
}
