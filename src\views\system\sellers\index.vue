<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="130px">
                  <el-form-item label="店铺名称" prop="storeName">
                    <el-input
                        v-model="queryParams.storeName"
                        placeholder="请输入店铺名称"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="店铺标识符" prop="storeSlug">
                    <el-input
                        v-model="queryParams.storeSlug"
                        placeholder="请输入店铺标识符"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <!-- <el-form-item label="店铺Logo链接" prop="logo">
                    <el-input
                        v-model="queryParams.logo"
                        placeholder="请输入店铺Logo链接"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="店铺封面图片链接" prop="coverImage">
                    <el-input
                        v-model="queryParams.coverImage"
                        placeholder="请输入店铺封面图片链接"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:sellers:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:sellers:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:sellers:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:sellers:export']"
        >导出</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Star"
            @click="handleRecommendSellers"
        >推荐商家</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="sellersList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <!-- <el-table-column label="序号" align="center" prop="sellerId" v-if="columns[0].visible" :show-overflow-tooltip="true"/> -->
                  <el-table-column label="店铺名称" align="center" prop="storeName" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺标识符" align="center" prop="storeSlug" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺Logo链接" align="center" prop="logo" v-if="columns[4].visible" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <img :src="scope.row.logo" alt="logo" style="width:40px;height:40px;object-fit:cover;border-radius:6px;border:1px solid #eee;background:#fafbfc;cursor:pointer;" v-if="scope.row.logo" @click="handleImagePreview(scope.row.logo)" />
                  </template>
                </el-table-column>
                <el-table-column label="店铺封面图片链接" align="center" prop="coverImage" v-if="columns[5].visible" :show-overflow-tooltip="true">
                  <template #default="scope">
                    <img :src="scope.row.coverImage" alt="cover" style="width:56px;height:40px;object-fit:cover;border-radius:6px;border:1px solid #eee;background:#fafbfc;cursor:pointer;" v-if="scope.row.coverImage" @click="handleImagePreview(scope.row.coverImage)" />
                  </template>
                </el-table-column>
                <el-table-column label="店铺描述" align="center" prop="description" v-if="columns[6].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺联系邮箱" align="center" prop="contactEmail" v-if="columns[7].visible" :show-overflow-tooltip="true"/>
                <!-- <el-table-column label="店铺联系电话" align="center" prop="contactPhone" v-if="columns[8].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺地址" align="center" prop="address" v-if="columns[9].visible" :show-overflow-tooltip="true"/> -->
                <el-table-column label="店铺验证状态" align="center" v-if="columns[9]?.visible">
                  <template #default="scope">
                    <div class="status-dot-container">
                      <span class="status-dot" :class="scope.row.verificationStatus || 'unverified'"></span>
                      <span>{{ 
                        scope.row.verificationStatus === 'verified' ? '已验证' : 
                        scope.row.verificationStatus === 'pending' ? '审核中' : 
                        scope.row.verificationStatus === 'unverified' ? '未验证' : '未知' 
                      }}</span>
                    </div>
                  </template>
                </el-table-column>
                  <el-table-column label="平台专属折扣码" align="center" prop="platformDiscountCode" v-if="columns[14]?.visible" :show-overflow-tooltip="true"/>
                  <el-table-column label="店铺状态" align="center" prop="status" v-if="columns[16]?.visible" :show-overflow-tooltip="true"/>
                <el-table-column label="店铺等级" align="center" prop="tierLevel" v-if="columns[17]?.visible" :show-overflow-tooltip="true"/>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:sellers:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:sellers:remove']">删除</el-button>
          <el-button link type="primary" icon="ShoppingBag" @click="handleShowProducts(scope.row)">展示商品</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改商家管理对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="sellersRef" :model="form" :rules="rules" label-width="130px">
                        <el-form-item label="店铺名称" prop="storeName">
                          <el-input v-model="form.storeName" placeholder="请输入店铺名称" />
                        </el-form-item>
                        <el-form-item label="店铺标识符" prop="storeSlug">
                          <el-input v-model="form.storeSlug" placeholder="请输入店铺标识符" />
                        </el-form-item>
                        <el-form-item label="店铺Logo" prop="logo">
                          <image-upload v-model="form.logo" :limit="1" />
                        </el-form-item>
                        <el-form-item label="店铺封面图片" prop="coverImage">
                          <image-upload v-model="form.coverImage" :limit="1" />
                        </el-form-item>
                        <el-form-item label="店铺描述" prop="description">
                          <el-input v-model="form.description" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="店铺联系邮箱" prop="contactEmail">
                          <el-input v-model="form.contactEmail" placeholder="请输入店铺联系邮箱" />
                        </el-form-item>
                        <el-form-item label="店铺联系电话" prop="contactPhone">
                          <el-input v-model="form.contactPhone" placeholder="请输入店铺联系电话" />
                        </el-form-item>
                        <el-form-item label="店铺地址" prop="address">
                          <el-input v-model="form.address" type="textarea" placeholder="请输入内容" />
                        </el-form-item>
                        <el-form-item label="店铺验证状态" prop="verificationStatus">
                          <el-select v-model="form.verificationStatus" placeholder="请选择验证状态">
                            <el-option label="已验证" value="verified" />
                            <el-option label="审核中" value="pending" />
                            <el-option label="未验证" value="unverified" />
                          </el-select>
                        </el-form-item>
                        <el-form-item label="平台专属折扣码" prop="platformDiscountCode">
                          <el-input v-model="form.platformDiscountCode" placeholder="请输入平台专属折扣码" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 推荐商家对话框 -->
    <el-dialog v-model="recommendSellersOpen" title="设置推荐商家（最多选择4个）" width="900px" append-to-body>
      <el-form ref="recommendSellersRef" :model="recommendSellersForm" label-width="100px">
        <el-form-item label="选择商家" prop="sellerIds">
          <el-select
            v-model="recommendSellersForm.sellerIds"
            multiple
            filterable
            remote
            :remote-method="handleRecommendSellerSearch"
            placeholder="请输入店铺名称搜索(最多选择4个)"
            :loading="recommendSellerSearchLoading"
            style="width: 100%"
            :default-first-option="true"
            clearable
            :multiple-limit="4"
          >
            <el-option
              v-for="item in recommendSellerOptions"
              :key="item.sellerId"
              :label="item.storeName"
              :value="item.sellerId"
            >
              <div class="recommend-seller-option">
                <img :src="item.coverImage" class="recommend-seller-cover" />
                <div class="recommend-seller-info">
                  <div class="recommend-seller-title">{{ item.storeName }}</div>
                  <div class="recommend-seller-desc">{{ item.description }}</div>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="已选商家" v-if="selectedRecommendSellers.length > 0">
          <div class="recommend-selected-list">
            <div v-for="(seller, index) in selectedRecommendSellers" :key="seller.sellerId" class="recommend-selected-item">
              <img :src="seller.coverImage" class="recommend-selected-cover" />
              <div class="recommend-selected-info">
                <div class="recommend-selected-title">{{ seller.storeName }}</div>
                <div class="recommend-selected-desc">{{ seller.description }}</div>
              </div>
              <div class="recommend-order-input">
                <el-input-number 
                  v-model="seller.displayOrder" 
                  :min="0" 
                  size="small" 
                  controls-position="right"
                  @change="updateSellerOrder(index, $event)"
                  placeholder="排序值"
                  class="order-input-number"
                />
              </div>
            </div>
          </div>
          <div class="recommend-order-tip">排序值越小，显示越靠前（0-99）</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="recommendSelectedSellers" :disabled="!selectedRecommendSellers.length">批量推荐</el-button>
          <el-button @click="recommendSellersOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 图片预览弹窗 -->
    <el-dialog v-model="imagePreviewVisible" width="auto" top="10vh" :show-close="true" append-to-body>
      <img :src="imagePreviewUrl" style="max-width:80vw;max-height:70vh;display:block;margin:auto;border-radius:10px;box-shadow:0 2px 16px rgba(0,0,0,0.18);background:#fff;" />
    </el-dialog>

    <!-- 展示商品对话框 -->
    <el-dialog v-model="showProductsOpen" title="设置展示商品（最多选择10个）" width="900px" append-to-body>
      <el-form ref="showProductsRef" :model="showProductsForm" label-width="100px">
        <el-form-item label="商家" prop="sellerInfo">
          <div class="seller-info-display">
            <img :src="selectedSeller.logo" class="seller-logo" />
            <div class="seller-name">{{ selectedSeller.storeName }}</div>
          </div>
        </el-form-item>
        <el-form-item label="选择商品" prop="productIds">
          <el-select
            v-model="showProductsForm.productIds"
            multiple
            filterable
            remote
            :remote-method="handleProductSearch"
            placeholder="请输入商品名称搜索(最多选择10个)"
            :loading="productSearchLoading"
            style="width: 100%"
            :default-first-option="true"
            clearable
            :multiple-limit="10"
          >
            <el-option
              v-for="item in productOptions"
              :key="item.productId"
              :label="item.name"
              :value="item.productId"
            >
              <div class="product-option">
                <img :src="item.mainImage" class="product-cover" />
                <div class="product-info">
                  <div class="product-title">{{ item.name }}</div>
                  <div class="product-price">{{ item.price ? '¥' + item.price : '价格未设置' }}</div>
                  <div class="product-desc">{{ item.description || '暂无描述' }}</div>
                </div>
              </div>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="已选商品" v-if="selectedProducts.length > 0">
          <div class="product-selected-list">
            <div v-for="(product, index) in selectedProducts" :key="product.productId" class="product-selected-item">
              <img :src="product.mainImage" class="product-selected-cover" />
              <div class="product-selected-info">
                <div class="product-selected-title">{{ product.name }}</div>
                <div class="product-selected-price">{{ product.price ? '¥' + product.price : '价格未设置' }}</div>
                <div class="product-selected-desc">{{ product.description || '暂无描述' }}</div>
              </div>
              <div class="product-order-input">
                <el-input-number 
                  v-model="product.displayOrder" 
                  :min="0" 
                  :max="99"
                  size="small" 
                  controls-position="right"
                  @change="updateProductOrder(index, $event)"
                  placeholder="排序值"
                  class="order-input-number"
                />
              </div>
            </div>
          </div>
          <div class="product-order-tip">排序值越小，显示越靠前（0-99）</div>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveSelectedProducts" :disabled="!selectedProducts.length">保存展示商品</el-button>
          <el-button @click="showProductsOpen = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Sellers">
  import { listSellers, getSellers, delSellers, addSellers, updateSellers, recommendSellers, getRecommendedSellers } from "@/api/system/sellers";
  import { listProducts, getProducts, updateProductStatus, getSellerDisplayProducts, setSellerDisplayProducts, getSellerProducts } from "@/api/system/products";

  const { proxy } = getCurrentInstance();

  const sellersList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    storeName: null,
                    storeSlug: null,
                    logo: null,
                    coverImage: null,
                    verificationStatus: null,
                    status: null,
                    tierLevel: null
    },
    rules: {
                    storeName: [
                { required: true, message: "店铺名称不能为空", trigger: "blur" }
              ],
                    storeSlug: [
                { required: true, message: "店铺标识符不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '卖家唯一标识', visible: true },
                { key: 1, label: '店铺名称', visible: true },
                { key: 2, label: '店铺标识符', visible: true },
                { key: 3, label: '店铺Logo链接', visible: true },
                { key: 4, label: '店铺封面图片链接', visible: true },
                { key: 5, label: '店铺描述', visible: true },
                { key: 6, label: '店铺联系邮箱', visible: true },
                { key: 7, label: '店铺联系电话', visible: true },
                { key: 8, label: '店铺地址', visible: true },
                { key: 9, label: '店铺验证状态', visible: true },
                { key: 10, label: '平台专属折扣码', visible: true },
                { key: 11, label: '店铺状态', visible: true },
                { key: 12, label: '店铺等级', visible: true },
                
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  const recommendSellersOpen = ref(false);
  const recommendSellerSearchLoading = ref(false);
  const recommendSellerOptions = ref([]);
  const recommendSellersForm = ref({ sellerIds: [] });
  const selectedRecommendSellers = ref([]);

  const imagePreviewUrl = ref("");
  const imagePreviewVisible = ref(false);

  const showProductsOpen = ref(false);
  const productSearchLoading = ref(false);
  const productOptions = ref([]);
  const showProductsForm = ref({ productIds: [] });
  const selectedProducts = ref([]);
  const selectedSeller = ref({});

  /** 查询商家管理列表 */
  function getList() {
    loading.value = true;
    listSellers(queryParams.value).then(response => {
      // 确保验证状态字段正确处理
      sellersList.value = response.rows.map(item => {
        // 确保 verificationStatus 存在且为合法值
        if (!item.verificationStatus || 
            !['verified', 'pending', 'unverified'].includes(item.verificationStatus)) {
          item.verificationStatus = 'unverified';
        }
        return item;
      });
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    sellerId: null,
                    userId: null,
                    storeName: null,
                    storeSlug: null,
                    logo: null,
                    coverImage: null,
                    description: null,
                    contactEmail: null,
                    contactPhone: null,
                    address: null,
                    rating: null,
                    totalRatings: null,
                    verificationStatus: null,
                    joinedDate: null,
                    platformDiscountCode: null,
                    platformUrl: null,
                    status: null,
                    tierLevel: null
    };
    proxy.resetForm("sellersRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.sellerId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加商家管理";
  }

  /** 修改按钮操作 */
function handleUpdate(row) {
  reset();
    const _sellerId = row.sellerId || ids.value
    getSellers(_sellerId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改商家管理";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["sellersRef"].validate(valid => {
      if (valid) {
        // 处理logo和coverImage为字符串
        if (Array.isArray(form.value.logo)) {
          form.value.logo = form.value.logo[0] || '';
        }
        if (Array.isArray(form.value.coverImage)) {
          form.value.coverImage = form.value.coverImage[0] || '';
        }
        // 确保验证状态字段正确
        if (!form.value.verificationStatus || 
            !['verified', 'pending', 'unverified'].includes(form.value.verificationStatus)) {
          form.value.verificationStatus = 'unverified';
        }
        
        if (form.value.sellerId != null) {
          updateSellers(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addSellers(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _sellerIds = row.sellerId || ids.value;
    proxy.$modal.confirm('是否确认删除商家管理编号为"' + _sellerIds + '"的数据项？').then(function() {
      return delSellers(_sellerIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/sellers/export', {
      ...queryParams.value
    }, `sellers_${new Date().getTime()}.xlsx`)
  }

  function handleRecommendSellerSearch(query) {
    recommendSellerSearchLoading.value = true;
    return listSellers({
      pageNum: 1,
      pageSize: 100,
      storeName: query
    }).then(response => {
      recommendSellerOptions.value = response.rows;
      recommendSellerSearchLoading.value = false;
    });
  }

  function handleRecommendSellers() {
    recommendSellersOpen.value = true;
    // 先获取已推荐商家
    getRecommendedSellers().then(res => {
      const recommended = res.data || [];
      recommendSellersForm.value.sellerIds = recommended.map(item => item.sellerId);
      // 查出所有商家，并在加载完后赋值
      handleRecommendSellerSearch('').then(() => {
        selectedRecommendSellers.value = recommendSellerOptions.value
          .filter(item => recommendSellersForm.value.sellerIds.includes(item.sellerId))
          .map(item => {
            const found = recommended.find(r => r.sellerId === item.sellerId);
            return {
              ...item,
              displayOrder: found ? found.displayOrder : 0
            };
          });
      });
    });
  }

  // 监听商家选择变化，处理删除操作
  watch(() => recommendSellersForm.value.sellerIds, (newVal) => {
    if (!newVal) return;
    
    // 保存当前已选商家的排序值
    const orderMap = {};
    selectedRecommendSellers.value.forEach(item => {
      orderMap[item.sellerId] = item.displayOrder || 0;
    });
    
    // 更新已选商家列表
    selectedRecommendSellers.value = recommendSellerOptions.value
      .filter(item => newVal.includes(item.sellerId))
      .map(item => ({
        ...item,
        // 保留已设置的排序值
        displayOrder: orderMap[item.sellerId] !== undefined ? orderMap[item.sellerId] : 0
      }));
  });

  function recommendSeller(seller) {
    proxy.$modal.msgSuccess(`已推荐商家：${seller.storeName}`);
    // 这里可以调用后端推荐接口
  }

  // 更新商家排序值
  function updateSellerOrder(index, value) {
    selectedRecommendSellers.value[index].displayOrder = value;
  }

  // 批量推荐选中的商家
  function recommendSelectedSellers() {
    if (selectedRecommendSellers.value.length === 0) {
      proxy.$modal.msgError("请至少选择一个商家");
      return;
    }
    
    // 构建包含ID和排序的数据结构
    const recommendData = selectedRecommendSellers.value.map(item => ({
      sellerId: item.sellerId,
      displayOrder: item.displayOrder || 0
    }));
    
    // 调用后端推荐接口
    recommendSellers(recommendData).then(response => {
      proxy.$modal.msgSuccess("推荐成功");
      recommendSellersOpen.value = false;
    }).catch(() => {
      proxy.$modal.msgError("推荐失败，请重试");
    });
  }

  function handleImagePreview(url) {
    imagePreviewUrl.value = url;
    imagePreviewVisible.value = true;
  }

  /** 展示商品按钮操作 */
  function handleShowProducts(row) {
    selectedSeller.value = row;
    showProductsOpen.value = true;
    showProductsForm.value.productIds = [];
    selectedProducts.value = [];
    productSearchLoading.value = true;
    
    // 获取该商家的所有商品
    handleProductSearch('');
  }

  // 商品搜索方法
  function handleProductSearch(query) {
    productSearchLoading.value = true;
    return getSellerProducts(selectedSeller.value.sellerId).then(response => {
      // 确保response是数组，如果不是则获取其中的数组属性
      const products = Array.isArray(response) ? response : (response.rows || response.data || []);
      
      // 如果查询词存在，进行本地过滤
      if (query) {
        productOptions.value = products.filter(item => 
          item.name && item.name.toLowerCase().includes(query.toLowerCase()) || 
          (item.description && item.description.toLowerCase().includes(query.toLowerCase()))
        );
      } else {
        productOptions.value = products;
      }
      
      productSearchLoading.value = false;
      
      // 获取已设置为展示的商品IDs
      getSellerDisplayProducts(selectedSeller.value.sellerId).then(res => {
        const displayProducts = res.data || [];
        showProductsForm.value.productIds = displayProducts.map(item => item.productId);
        
        // 设置已选商品
        if (Array.isArray(productOptions.value)) {
          selectedProducts.value = productOptions.value
            .filter(item => displayProducts.map(dp => dp.productId).includes(item.productId))
            .map(product => {
              // 查找对应的展示产品记录，获取排序值
              const displayProduct = displayProducts.find(dp => dp.productId === product.productId);
              return {
                ...product,
                displayOrder: displayProduct ? displayProduct.displayOrder : 0
              };
            });
        }
      });
    }).catch(error => {
      console.error('获取商品列表失败:', error);
      productSearchLoading.value = false;
      productOptions.value = [];
    });
  }

  // 监听商品选择变化
  watch(() => showProductsForm.value.productIds, (newVal) => {
    if (!newVal) return;
    
    // 更新已选商品列表
    selectedProducts.value = productOptions.value
      .filter(item => newVal.includes(item.productId))
      .map(product => {
        // 保留现有产品的排序值，如果有的话
        const existingProduct = selectedProducts.value.find(p => p.productId === product.productId);
        return {
          ...product,
          displayOrder: existingProduct ? existingProduct.displayOrder : 0
        };
      });
  });

  // 保存选中的商品
  function saveSelectedProducts() {
    if (selectedProducts.value.length === 0) {
      proxy.$modal.msgError("请至少选择一个商品");
      return;
    }
    
    // 构建商品ID列表和排序值
    const products = selectedProducts.value.map(item => ({
      productId: item.productId,
      displayOrder: item.displayOrder || 0,
      sellerId: selectedSeller.value.sellerId // 直接在每个商品对象中包含商家ID
    }));
    
    // 直接发送商品数组
    setSellerDisplayProducts(products).then(response => {
      proxy.$modal.msgSuccess("保存成功");
      showProductsOpen.value = false;
    }).catch((error) => {
      console.error('保存失败:', error);
      proxy.$modal.msgError("保存失败，请重试");
    });
  }

  // 更新商品排序值
  function updateProductOrder(index, value) {
    selectedProducts.value[index].displayOrder = value;
  }

  getList();
</script>




<style>
.status-dot-container {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.status-dot {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.active {
  background-color: #67C23A; /* 绿色 */
}

.status-dot.inactive {
  background-color: #E6A23C; /* 黄色 */
}

.status-dot.suspended {
  background-color: #F56C6C; /* 红色 */
}

.status-dot.verified {
  background-color: #67C23A; /* 绿色 */
}

.status-dot.pending {
  background-color: #E6A23C; /* 黄色 */
}

.status-dot.unverified {
  background-color: #909399; /* 灰色 */
}

.recommend-seller-option {
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 48px;
}
.recommend-seller-cover {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #eee;
  background: #fafbfc;
  flex-shrink: 0;
}
.recommend-seller-info {
  flex: 1;
  min-width: 0;
}
.recommend-seller-title {
  font-weight: bold;
  font-size: 15px;
  color: #222;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recommend-seller-desc {
  font-size: 13px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recommend-selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
}
.recommend-selected-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 12px 18px;
  min-width: 320px;
  max-width: 400px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
  gap: 16px;
}
.recommend-selected-cover {
  width: 64px;
  height: 64px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #eee;
  background: #fafbfc;
}
.recommend-selected-info {
  flex: 1;
  min-width: 0;
}
.recommend-selected-title {
  font-weight: bold;
  font-size: 17px;
  color: #222;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recommend-selected-desc {
  font-size: 13px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.recommend-btn {
  margin-left: 12px;
  font-weight: bold;
  letter-spacing: 1px;
}
@media (max-width: 900px) {
  .recommend-selected-list {
    flex-direction: column;
    gap: 10px;
  }
  .recommend-selected-item {
    min-width: 0;
    max-width: 100%;
  }
}
.recommend-order-input {
  width: 80px;
  margin-left: auto;
  display: flex;
  align-items: center;
}

.order-input-number {
  width: 80px;
}

.recommend-order-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  text-align: right;
}

/* 优化推荐商家下拉项图片显示 */
.el-select-dropdown__item {
  height: auto !important;
  min-height: 56px !important;
  padding-top: 6px !important;
  padding-bottom: 6px !important;
}
.el-select-dropdown__item.selected,
.el-select-dropdown__item.hover {
  background: #f5f7fa !important;
}

/* 展示商品相关样式 */
.seller-info-display {
  display: flex;
  align-items: center;
  gap: 12px;
}

.seller-logo {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 50%;
  border: 1px solid #eee;
}

.seller-name {
  font-weight: bold;
  font-size: 16px;
}

.product-option {
  display: flex;
  align-items: center;
  gap: 12px;
  min-height: 48px;
}
.product-cover {
  width: 48px;
  height: 48px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #eee;
  background: #fafbfc;
  flex-shrink: 0;
}
.product-info {
  flex: 1;
  min-width: 0;
}
.product-title {
  font-weight: bold;
  font-size: 15px;
  color: #222;
  margin-bottom: 2px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-price {
  font-size: 14px;
  color: #f56c6c;
  font-weight: 500;
  margin-bottom: 2px;
}
.product-desc {
  font-size: 13px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-selected-list {
  display: flex;
  flex-wrap: wrap;
  gap: 18px;
}
.product-selected-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 12px 18px;
  min-width: 320px;
  max-width: 400px;
  margin-bottom: 8px;
  box-shadow: 0 2px 8px 0 rgba(0,0,0,0.03);
  gap: 16px;
}
.product-selected-cover {
  width: 64px;
  height: 64px;
  object-fit: cover;
  border-radius: 8px;
  border: 1px solid #eee;
  background: #fafbfc;
}
.product-selected-info {
  flex: 1;
  min-width: 0;
}
.product-selected-title {
  font-weight: bold;
  font-size: 17px;
  color: #222;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-selected-price {
  font-size: 15px;
  color: #f56c6c;
  font-weight: 500;
  margin-bottom: 4px;
}
.product-selected-desc {
  font-size: 13px;
  color: #888;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.product-order-input {
  width: 80px;
  margin-left: auto;
  display: flex;
  align-items: center;
}

.order-input-number {
  width: 80px;
}

.product-order-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 8px;
  text-align: right;
}
</style>


