import request from '@/utils/request'

// 手动执行商品状态检查
export function executeProductStatusCheck() {
  return request({
    url: '/system/productStatusCheck/execute',
    method: 'post',
    timeout: 300000 // 5分钟超时，因为全量检查可能需要较长时间
  })
}

// 检查单个商品状态
export function checkSingleProductStatus(sku, platform) {
  return request({
    url: '/system/productStatusCheck/checkSingle',
    method: 'get',
    params: { sku, platform },
    timeout: 60000 // 1分钟超时
  })
}

// 分页检查商品状态
export function executePageProductStatusCheck(pageSize = 100, pageNum = 1) {
  return request({
    url: '/system/productStatusCheck/executePage',
    method: 'post',
    params: { pageSize, pageNum },
    timeout: 180000 // 3分钟超时
  })
}

// 配置定时任务
export function configSchedule(data) {
  return request({
    url: '/system/productStatusCheck/configSchedule',
    method: 'post',
    data: data
  })
}

// 获取定时任务配置
export function getScheduleConfig() {
  return request({
    url: '/system/productStatusCheck/getScheduleConfig',
    method: 'get'
  })
}

// 启用/禁用定时任务
export function toggleSchedule(enabled) {
  return request({
    url: '/system/productStatusCheck/toggleSchedule',
    method: 'post',
    params: { enabled }
  })
}

// 删除定时任务
export function removeSchedule() {
  return request({
    url: '/system/productStatusCheck/removeSchedule',
    method: 'delete'
  })
}

// 获取检查历史记录（扩展功能）
export function getCheckHistory(query) {
  return request({
    url: '/system/productStatusCheck/history',
    method: 'get',
    params: query
  })
}

// 获取检查统计信息（扩展功能）
export function getCheckStatistics(dateRange) {
  return request({
    url: '/system/productStatusCheck/statistics',
    method: 'get',
    params: dateRange
  })
}
