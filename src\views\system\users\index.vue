<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" label-width="68px">
                  <el-form-item label="用户名" prop="username">
                    <el-input
                        v-model="queryParams.username"
                        placeholder="请输入用户名"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="用户邮箱" prop="email">
                    <el-input
                        v-model="queryParams.email"
                        placeholder="请输入用户邮箱"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <!-- <el-form-item label="用户密码" prop="password">
                    <el-input
                        v-model="queryParams.password"
                        placeholder="请输入用户密码"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item> -->
                  <el-form-item label="用户名字" prop="firstName">
                    <el-input
                        v-model="queryParams.firstName"
                        placeholder="请输入用户名字"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <el-form-item label="用户姓氏" prop="lastName">
                    <el-input
                        v-model="queryParams.lastName"
                        placeholder="请输入用户姓氏"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item>
                  <!-- <el-form-item label="用户联系电话" prop="phoneNumber">
                    <el-input
                        v-model="queryParams.phoneNumber"
                        placeholder="请输入用户联系电话"
                        clearable
                        @keyup.enter="handleQuery"
                    />
                  </el-form-item> -->
      <el-form-item>
        <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:users:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="success"
            plain
            icon="Edit"
            :disabled="single"
            @click="handleUpdate"
            v-hasPermi="['system:users:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="multiple"
            @click="handleDelete"
            v-hasPermi="['system:users:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
            type="warning"
            plain
            icon="Download"
            @click="handleExport"
            v-hasPermi="['system:users:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar v-model:showSearch="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="usersList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
              <!-- 按行排序 -->
              <el-table-column label="排序" type="index" width="80" align="center" />
              <!-- <el-table-column label="用户唯一标识" align="center" prop="userId" v-if="columns[0].visible" :show-overflow-tooltip="true"/> -->
                <el-table-column label="用户名" align="center" prop="username" v-if="columns[1].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户邮箱" align="center" prop="email" v-if="columns[2].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户密码" align="center" prop="password" v-if="columns[3].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户名字" align="center" prop="firstName" v-if="columns[4].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户姓氏" align="center" prop="lastName" v-if="columns[5].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户头像" align="center" prop="avatarUrl" width="100" v-if="columns[6].visible">
                <template #default="scope">
                  <image-preview :src="scope.row.avatarUrl" :width="50" :height="50"/>
                </template>
              </el-table-column>
                <el-table-column label="账户创建时间" align="center" prop="createdAt" width="180" v-if="columns[7].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.createdAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                  <el-table-column label="用户最后登录时间" align="center" prop="lastLoginAt" width="180" v-if="columns[9].visible" :show-overflow-tooltip="true">
                <template #default="scope">
                  <span>{{ parseTime(scope.row.lastLoginAt, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                </template>
              </el-table-column>
                <el-table-column label="用户状态" align="center" prop="status" v-if="columns[10].visible" :show-overflow-tooltip="true"/>
                <el-table-column label="用户电话" align="center" prop="phoneNumber" v-if="columns[12]?.visible" :show-overflow-tooltip="true"/>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template #default="scope">
          <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)" v-hasPermi="['system:users:edit']">修改</el-button>
          <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)" v-hasPermi="['system:users:remove']">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
        v-show="total>0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
    />

    <!-- 添加或修改用户信息对话框 -->
    <el-dialog :title="title" v-model="open" width="500px" append-to-body>
      <el-form ref="usersRef" :model="form" :rules="rules" label-width="80px">
                        <el-form-item label="用户名" prop="username">
                          <el-input v-model="form.username" placeholder="请输入用户名" />
                        </el-form-item>
                        <el-form-item label="用户邮箱" prop="email">
                          <el-input v-model="form.email" placeholder="请输入用户邮箱" />
                        </el-form-item>
                        <el-form-item label="用户密码" prop="password">
                          <el-input v-model="form.password" placeholder="请输入用户密码" />
                        </el-form-item>
                        <el-form-item label="用户名字" prop="firstName">
                          <el-input v-model="form.firstName" placeholder="请输入用户名字" />
                        </el-form-item>
                        <el-form-item label="用户姓氏" prop="lastName">
                          <el-input v-model="form.lastName" placeholder="请输入用户姓氏" />
                        </el-form-item>
                        <el-form-item label="用户头像" prop="avatarUrl">
                          <image-upload v-model="form.avatarUrl"/>
                        </el-form-item>
                        <el-form-item label="用户电话" prop="phoneNumber">
                          <el-input v-model="form.phoneNumber" placeholder="请输入用户联系电话" />
                        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Users">
  import { listUsers, getUsers, delUsers, addUsers, updateUsers } from "@/api/system/users";

  const { proxy } = getCurrentInstance();

  const usersList = ref([]);
  const open = ref(false);
  const loading = ref(true);
  const showSearch = ref(true);
  const ids = ref([]);
  const single = ref(true);
  const multiple = ref(true);
  const total = ref(0);
  const title = ref("");

  const data = reactive({
    form: {},
    queryParams: {
      pageNum: 1,
      pageSize: 10,
                    username: null,
                    email: null,
                    password: null,
                    firstName: null,
                    lastName: null,
                    status: null,
                    phoneNumber: null,
    },
    rules: {
                    username: [
                { required: true, message: "用户名不能为空", trigger: "blur" }
              ],
                    email: [
                { required: true, message: "用户邮箱不能为空", trigger: "blur" }
              ],
                    password: [
                { required: true, message: "用户密码不能为空", trigger: "blur" }
              ],
                    firstName: [
                { required: true, message: "用户名字不能为空", trigger: "blur" }
              ],
    },
    //表格展示列
    columns: [
              { key: 0, label: '用户唯一标识', visible: true },
                { key: 1, label: '用户名', visible: true },
                { key: 2, label: '用户邮箱', visible: true },
                { key: 3, label: '用户密码', visible: true },
                { key: 4, label: '用户名字', visible: true },
                { key: 5, label: '用户姓氏', visible: true },
                { key: 6, label: '用户头像', visible: true },
                { key: 7, label: '账户创建时间', visible: true },
                { key: 8, label: '用户最后登录时间', visible: true },
                { key: 9, label: '用户状态', visible: true },
                { key: 10, label: '用户电话', visible: true },
      ],
  });

  const { queryParams, form, rules,columns } = toRefs(data);

  /** 查询用户信息列表 */
  function getList() {
    loading.value = true;
    listUsers(queryParams.value).then(response => {
            usersList.value = response.rows;
      total.value = response.total;
      loading.value = false;
    });
  }

  // 取消按钮
  function cancel() {
    open.value = false;
    reset();
  }

  // 表单重置
  function reset() {
    form.value = {
                    userId: null,
                    username: null,
                    email: null,
                    password: null,
                    firstName: null,
                    lastName: null,
                    avatarUrl: null,
                    createdAt: null,
                    updatedAt: null,
                    lastLoginAt: null,
                    status: null,
                    bio: null,
                    phoneNumber: null,
                    role: null
    };
    proxy.resetForm("usersRef");
  }

  /** 搜索按钮操作 */
  function handleQuery() {
    queryParams.value.pageNum = 1;
    getList();
  }

  /** 重置按钮操作 */
  function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
  }

  // 多选框选中数据
  function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.userId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
  }

  /** 新增按钮操作 */
  function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加用户信息";
  }

  /** 修改按钮操作 */
  function handleUpdate(row) {
    reset();
    const _userId = row.userId || ids.value
    getUsers(_userId).then(response => {
      form.value = response.data;
      open.value = true;
      title.value = "修改用户信息";
    });
  }

  /** 提交按钮 */
  function submitForm() {
    proxy.$refs["usersRef"].validate(valid => {
      if (valid) {
        if (form.value.userId != null) {
          updateUsers(form.value).then(response => {
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          });
        } else {
          addUsers(form.value).then(response => {
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          });
        }
      }
    });
  }

  /** 删除按钮操作 */
  function handleDelete(row) {
    const _userIds = row.userId || ids.value;
    proxy.$modal.confirm('是否确认删除用户信息编号为"' + _userIds + '"的数据项？').then(function() {
      return delUsers(_userIds);
    }).then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    }).catch(() => {});
  }

  /** 导出按钮操作 */
  function handleExport() {
    proxy.download('system/users/export', {
      ...queryParams.value
    }, `users_${new Date().getTime()}.xlsx`)
  }

  getList();
</script>
